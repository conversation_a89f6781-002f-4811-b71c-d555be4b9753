<div align="center">
<img src="https://s2.loli.net/2023/10/27/WzQ4JLNV5epKh6X.png" style="width:150px"/>
    <h1>Nova Admin</h1>
</div>

<div align="center">
    <img src="https://img.shields.io/github/license/chansee97/nova-admin"/>
    <img src="https://badgen.net/github/stars/chansee97/nova-admin?icon=github"/>
    <img src="https://gitee.com/chansee97/nova-admin/badge/star.svg"/>
    <img src="https://img.shields.io/github/forks/chansee97/nova-admin"/>
</div>

<div align='center'>

  English |  [中文](./README.zh-CN.md)
</div>

## Introduction

[Nova-admin](https://github.com/chansee97/nova-admin) is a clean and concise back-end management template based on Vue3, Vite5, Typescript, and Naive UI. It implements complete functionality in a simple way, while also considering code standards, readability, and avoiding excessive encapsulation to facilitate secondary development.

- [Nova-Admin preview](https://nova-admin.pages.dev/)
- [Nova-Admin docs](https://nova-admin-docs.pages.dev/)

## Features

- Developed based on the latest technology stack including Vue3, Vite5, TypeScript, NaiveUI, Unocss, etc.
- Based on [alova](https://alova.js.org/) encapsulation and configuration, providing unified response handling and multi-scenario capabilities.
- Comprehensive front-end and back-end permission management solution.
- Supports local static routes and dynamically generated routes from the back end, with easy route configuration.
- Secondary encapsulation of commonly used components to meet basic work requirements.
- Dark theme adaptation, maintaining the Naive style for interface aesthetics.
- Only performs eslint validation during submission without excessive restrictions for simpler development.
- Flexible and configurable interface style layout.
- Multilanguage (i18n) support.

## Project preview

![preview-1.png](https://s2.loli.net/2024/03/31/UZoajnEekMX9cLi.png)
![preview-2.jpg](https://s2.loli.net/2024/03/31/8VwMSyXWtO6avKg.jpg)
![preview-5.png](https://s2.loli.net/2024/03/31/TCtE2ZBU7MJ6HrY.png)
![preview-6.png](https://s2.loli.net/2024/03/31/YM1OrxZCimzLcd6.png)
![preview-4.png](https://s2.loli.net/2024/03/31/hgYHKjACqs7rcPV.png)
![preview-3.png](https://s2.loli.net/2024/03/31/TuYajAH9LWsMPf5.png)

## Repo

- [Gitee](https://gitee.com/chansee97/nova-admin)
- [Github](https://github.com/chansee97/nova-admin)

## Interface document

This project uses ApiFox for interface mock, check the online documentation for more interface details
[online aipfox docs](https://nova-admin.apifox.cn)

## Install and use

The local development environment is recommended to use pnpm 8.x, Node.js must be version 20.x.

It is recommended to directly download the compressed package from [Releases](https://github.com/chansee97/nova-admin/releases)

```bash
# install dependencies
pnpm i

# Run
pnpm dev

# Build product
pnpm build

```

## Related projects

- [Nova-admin-nest](https://github.com/chansee97/nove-admin-nest) (under development) Nova-Admin supporting background project based on TS, NestJs, typeorm

## Learn to communicate

Nova-Admin is a completely open-source and free project. It is still being optimized and iterated. It is designed to help developers more conveniently develop medium and large management systems. If you have any questions, please ask questions in the QQ exchange group.

| Q-Group | wechat-Group |wechat |
| :--: |:--: |:--: |
| <img src="https://cdn.jsdelivr.net/gh/chansee97/static/nova-admin/q-group.png" width=170> | <img src="https://cdn.jsdelivr.net/gh/chansee97/static/nova-admin/wx-group.png" width=170>|<img src="https://cdn.jsdelivr.net/gh/chansee97/static/wechat.png" width=170>|

> The WeChat group QR code is invalid, please add me as a friend.

## Contribution

If you find any issues or have suggestions for improvement, please create an [issue](nova-admin/issues/new) or submit a PR. We welcome your contributions!

## Support

If you feel that this project is helpful for your work or study, please help me order a ✨ Star, which will be a great encouragement and support for me, or you can buy me a cup of coffee below

| wechat | alipay |
| :--: |:--: |
| <img src="https://cdn.jsdelivr.net/gh/chansee97/static/sponsor-wechat.png" width=170> | <img src="https://cdn.jsdelivr.net/gh/chansee97/static/sponsor-alipay.png" width=170>|

## Contributors

Thanks for all their contributions!

<a href="https://github.com/chansee97/nova-admin/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=chansee97/nova-admin" alt="contributors" />
</a>

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=chansee97/nova-admin&type=Date)](https://star-history.com/#chansee97/nova-admin&Date)

## License

[MIT](LICENSE)
