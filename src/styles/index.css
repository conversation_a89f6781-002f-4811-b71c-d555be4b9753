@import './reset.css';
@import './transition.css';
@import './naive.css';
@import url('https://fonts.cdnfonts.com/css/inter');

:root{
  --theme-color: #7F9A66;
}

* {
  font-family: 'Inter', sans-serif;
}

html,
body,
#app {
  height: 100%;
}
.color-weak {
  filter: invert(80%);
}

.gray-mode {
  filter: grayscale(100%);
}

.overflow-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-muted {
  color: #615F5F;
}

.glass {
  background: rgba(255, 255, 255, 1) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(230, 230, 230, 0.7) !important;
  /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
}

.shadow {
  box-shadow: 0 4px 12px #e0ebe2cc;
}

.opacity-active {
  background: rgba(255, 255, 255, 0.6) !important;
}

.rounded-theme {
  border-radius: 20px !important;
}

.n-menu-item {
  margin-bottom:10px !important;
}

.n-submenu-children .n-menu-item{
  margin:0 !important;
}

.n-submenu-children .n-menu-item .n-menu-item-content-header a{
  font-weight: 500 !important;
}

.n-menu-item-content-header a{
  color: var(--theme-color) !important;
  font-weight: 700;
}

.n-menu-item-content__icon{
  color: var(--theme-color) !important;
}

.text-success, .text-theme { 
  color: var(--theme-color);
}

.bg-theme { 
  background: var(--theme-color);
}

.n-checkbox.n-checkbox--checked .n-checkbox-box {
  background: var(--theme-color);
}

.card-title {
  font-size:22px;
  font-weight:600;
}

.card-content {
  font-size:20px;
  font-weight:500;
}

.badge-pending {
  background: #B1E0F888;
  color:  #21b4ff;
}

.badge-approved { 
  background: #b9ffc388;
  color: #5cc864;
}

.badge-rejected {
  background: #ff9d9f88;
  color: #ff8082;
}

.vendor-card {
  border: 2px solid #f0f0f0 !important;
  box-shadow: 0 4px 12px #e0ebe2cc;
}