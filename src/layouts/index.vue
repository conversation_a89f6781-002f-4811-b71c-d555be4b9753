<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import leftMenu from './leftMenu.layout.vue'
import emptyMenu from './emptyMenu.layout.vue'
import topMenu from './topMenu.layout.vue'
import { SettingDrawer } from './components'
import testMenu from './testMenu.vue'
import { useAppStore } from '@/store/app'

const route = useRoute()
const appStore = useAppStore()

const layoutMap = {
  leftMenu,
  topMenu,
  testMenu,
  emptyMenu,
}

const withoutLeftMenuPaths = new Set([
  '/draw-results',
  '/check-in-results',
  '/ticket-preview',
  '/hall-layout',
  '/select-company',
  '/select-event',
  '/event-stepper',
  '/check-ins',
])

const currentLayout = computed(() => {
  if (withoutLeftMenuPaths.has(route.path)) {
    return 'emptyMenu'
  }

  return appStore.layoutMode
})
</script>

<template>
  <SettingDrawer />
  <component :is="layoutMap[currentLayout]" v-if="currentLayout" />
</template>
