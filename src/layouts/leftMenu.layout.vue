<script lang="ts" setup>
import {
  BackTop,
  // Breadcrumb,
  CollapaseButton,
  // FullScreen,
  Logo,
  Menu,
  // Notices,
  // Search,
  // Setting,
  TabBar,
  UserCenter,
} from './components'
import { useAppStore, useRouteStore } from '@/store'

const routeStore = useRouteStore()
const appStore = useAppStore()

const windowWidth = ref(window.innerWidth)
const prevWindowWidth = ref(window.innerWidth) // Track the previous window width

function preventRefresh(event) {
  if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
    event.preventDefault()
    appStore.reloadPage()
  }
}

function handleResize() {
  windowWidth.value = window.innerWidth
}

onMounted(async () => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', preventRefresh)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  window.addEventListener('keydown', preventRefresh)
})

watch(windowWidth, (newWidth) => {
  if (newWidth < 992 && prevWindowWidth.value >= 992 && !appStore.collapsed) {
    appStore.toggleCollapse()
  }
  if (newWidth >= 992 && prevWindowWidth.value < 992 && appStore.collapsed) {
    appStore.toggleCollapse()
  }

  prevWindowWidth.value = newWidth
})
</script>

<template>
  <n-layout
    has-sider
    class="wh-full"
    embedded
  >
    <n-layout-sider
      v-if="!appStore.contentFullScreen"
      :collapsed="appStore.collapsed"
      collapse-mode="width"
      :collapsed-width="90"
      :width="280"
      class="shadow px-3"
      content-style="display: flex;flex-direction: column;min-height:100%;"
    >
      <CollapaseButton />

      <Logo v-if="appStore.showLogo" />

      <n-scrollbar class="flex-1">
        <Menu class="mt-150px" />
      </n-scrollbar>

      <n-divider />

      <UserCenter />
    </n-layout-sider>

    <n-layout
      class="h-full flex flex-col bg-white"
      content-style="display: flex;flex-direction: column;min-height:100%;"
      :native-scrollbar="false"
    >
      <!-- <n-layout-header position="absolute" class="z-999">
        <div v-if="!appStore.contentFullScreen" class="h-60px flex-y-center justify-between">
          <div class="flex-y-center h-full">
            <CollapaseButton />
            <Breadcrumb />
          </div>
          <div class="flex-y-center gap-1 h-full p-x-xl">
            <Search />
            <FullScreen />
            <DarkModeSwitch />
            <LangsSwitch />
            <Notices />
            <Setting />
            <UserCenter />
          </div>
        </div>
      </n-layout-header> -->
      <!-- 196 = 16 + 115 + 60 45是面包屑高度 60是标签栏高度 -->
      <!-- 56 = 16 + 40 40是页脚高度 -->
      <div
        class="flex-1 flex flex-col bg-[#FAFBFF]"
        :class="{
          'm-t-0px': appStore.showTabs,
          'p-b-56px': appStore.showFooter && !appStore.contentFullScreen,
          'p-t-76px': !appStore.showTabs,
          'p-t-61px': appStore.contentFullScreen,
        }"
      >
        <TabBar class="h-80px z-999 bg-transparent px-6 md:px-16 xl:pe-28 pt-2" />

        <router-view v-slot="{ Component, route }" class="flex-1 mt-4">
          <transition
            :name="appStore.transitionAnimation"
            mode="out-in"
          >
            <keep-alive :include="routeStore.cacheRoutes">
              <component
                :is="Component"
                v-if="appStore.loadFlag"
                :key="route.fullPath"
                class="px-6 md:px-16 xl:pe-28 mb-4"
              />
            </keep-alive>
          </transition>
        </router-view>
      </div>
      <!-- <n-layout-footer
        v-if="appStore.showFooter && !appStore.contentFullScreen"
        position="absolute"
        class="h-40px flex-center"
      >
        {{ appStore.footerText }}
      </n-layout-footer> -->
      <BackTop />
    </n-layout>
  </n-layout>
</template>
