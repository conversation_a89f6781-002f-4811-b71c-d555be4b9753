<script setup lang="ts">
import SwitchCompany from '../modal/SwitchCompany.vue'
import { useAppStore, useAuthStore } from '@/store'
import { local, renderIcon } from '@/utils'
import IconLogout from '~icons/icon-park-outline/logout'
import IconBuildingOne from '~icons/icon-park-outline/building-one'
import { useBoolean } from '@/hooks'
import ConfirmModal from '@/views/ticket/ConfirmModal.vue'

// hooks & stores
const { t } = useI18n()
const router = useRouter()
const appStore = useAppStore()
const authStore = useAuthStore()
const { logout } = useAuthStore()

// visibility states
const { bool: switchCompanyModalVisible, setTrue: openSwitchCompanyModal, setFalse: closeSwitchCompanyModal } = useBoolean(false)
const { bool: changePasswordModalVisible, setTrue: openChangePasswordModal, setFalse: closeChangePasswordModal } = useBoolean(false)

// etc
const userInfo = ref()
const options = computed(() => {
  return [
    {
      label: 'Switch Company ',
      key: 'switchCompany',
      icon: () => h(IconBuildingOne),
    },
    {
      label: 'View License ',
      key: 'viewLicense',
      icon: renderIcon('mdi:license'),
    },
    {
      label: 'Change Password',
      key: 'changePassword',
      icon: renderIcon('icon-park-outline:lock'),
    },
    {
      type: 'divider',
      key: 'd1',
    },
    {
      label: t('app.loginOut'),
      key: 'loginOut',
      icon: () => h(IconLogout),
    },
  ]
})

async function handleChangePassword() {
  const { success, message } = await authStore.forgotPassword()
  if (success) {
    window.$message.success(message)
  }
  else {
    window.$message.error(message)
  }
}

function handleViewLicense() {
  router.push('/license')
}

function handleSelect(key: string | number) {
  if (key === 'loginOut') {
    window.$dialog?.success({
      title: t('app.loginOutTitle'),
      content: t('app.loginOutContent'),
      positiveText: t('common.confirm'),
      negativeText: t('common.cancel'),
      onPositiveClick: () => {
        logout()
      },
    })
  }
  if (key === 'userCenter')
    router.push('/userCenter')

  if (key === 'switchCompany')
    openSwitchCompanyModal()

  if (key === 'changePassword')
    openChangePasswordModal()

  if (key === 'viewLicense')
    handleViewLicense()
}

onBeforeMount(() => {
  userInfo.value = local.get('userInfo')
})
</script>

<template>
  <n-dropdown
    trigger="click"
    :options="options"
    @select="handleSelect"
  >
    <div class="flex mb-6 hover:cursor-pointer items-center justify-center gap-x-3">
      <div class="min-w-50px w-50px h-50px">
        <div class="rounded-10px bg-theme text-white h-100% flex justify-center items-center uppercase text-2xl font-medium">
          {{ userInfo.username?.substring(0, 1) }}
        </div>
      </div>

      <div v-if="!appStore.collapsed" class="hidden lg:flex flex-col flex-1">
        <n-ellipsis class="w-180px">
          <span class="fw-bold text-base">{{ userInfo.nickname }}</span>
        </n-ellipsis>
        <n-ellipsis class="w-180px">
          <span class="text-muted">{{ userInfo.email }}</span>
        </n-ellipsis>
      </div>
    </div>
  </n-dropdown>

  <SwitchCompany :visible="switchCompanyModalVisible" :close-modal="closeSwitchCompanyModal" :allow-cancel="true" />

  <ConfirmModal
    :show-modal="changePasswordModalVisible"
    title="Change Password"
    message="By confirming this action an email will be sent to your email address with a link to reset your password. Are you sure you want to proceed?"
    confirm-button-text="Yes, Send!"
    cancel-button-text="Cancel"
    @confirm="handleChangePassword"
    @cancel="closeChangePasswordModal"
    @update:show-modal="val => changePasswordModalVisible = val"
  />
</template>

<style scoped></style>
