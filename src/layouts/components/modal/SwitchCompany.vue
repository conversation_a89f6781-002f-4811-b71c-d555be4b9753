<script setup lang="ts">
import { computed, defineEmits, defineProps, onBeforeUnmount, onMounted, ref } from 'vue'
import { Icon } from '@iconify/vue'
import { useRouter } from 'vue-router'
import { $t } from '@/utils/i18n'
import { useCompanyStore } from '@/store/company'
import SearchIcon from '~icons/icon-park-outline/search'
import { useAppStore, useAuthStore } from '@/store'
import { useEventStore } from '@/store/event'
import { preDefinedColors } from '@/utils/default-values'

// interfaces
interface Props {
  visible: boolean
  closeModal: () => void
  allowCancel?: boolean
  allowLogout?: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

// emits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

// stores
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const { logout } = useAuthStore()
const eventStore = useEventStore()
const companyStore = useCompanyStore()

// etc
const loading = ref(false)
const predefinedColors = preDefinedColors()
const allowCancel = props.allowCancel ?? true
const allowLogout = props.allowLogout ?? true

async function fetchCompanyList(reset = false) {
  loading.value = true
  companyStore.companyList = []
  await companyStore.fetchCompanyList(reset)
  loading.value = false
}

function switchCompany(id: number) {
  localStorage.setItem('companyId', id.toString())
  companyStore.companyId = id

  window.$message.success('Switched company successfully!')
  eventStore.companyId = id
  eventStore.fetchEventList(true)

  if (eventStore.eventList.length < 1) {
    router.push('/event-stepper')
  }
  eventStore.selectedEvent = eventStore.eventList[0]

  props.closeModal()
  router.push('/dashboard')

  // console.log(route.name)

  if (route.name !== 'selectCompany') {
    window.location.reload()
  }

  // appStore.reloadPage()
}

function handleKeydown(event: KeyboardEvent) {
  if (allowCancel && event.key === 'Escape') { // Check allowCancel before closing
    props.closeModal()
  }
}

onMounted(async () => {
  await fetchCompanyList()
  window.addEventListener('keydown', handleKeydown)
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    class="w-550px min-h-400px max-w-90%"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="rounded-theme">
      <template #header>
        <div class="">
          {{ $t('company.selectCompany') }}
        </div>
        <div class="text-gray-4 line-height-tight mt-2 text-sm italic fw-300">
          {{ $t('company.switchCompanyMessage') }}
        </div>
        <n-input-group class="max-w-100% w-100% mt-4">
          <n-input v-model:value="companyStore.searchKeyword" :placeholder="`${$t('common.search')}`" size="large" clearable @keyup.enter="fetchCompanyList(true)">
            <template #prefix>
              <n-icon :component="SearchIcon" />
            </template>
          </n-input>
        </n-input-group>
      </template>
      <n-scrollbar class="max-h-300px pe-5">
        <n-flex v-if="loading" justify="center">
          <Icon icon="line-md:loading-loop" class="text-4xl animate-spin" :color="appStore.primaryColor" />
        </n-flex>
        <n-flex vertical>
          <n-flex v-for="item in companyStore.companyList" :key="item.id" class="border-b border-b-gray-2 p-3 cursor-pointer hover:bg-gray-1" @click="switchCompany(item.id)">
            <div class="w-75px h-75px rounded-full flex justify-center items-center" :style="{ backgroundColor: predefinedColors[0] }">
              <span class="text-3xl text-white fw-bold">{{ item.name[0] }}</span>
            </div>
            <div class="my-auto">
              <b>{{ item.name }}</b>
              <br>
              {{ item.email }}
            </div>
          </n-flex>
        </n-flex>
      </n-scrollbar>
      <template #footer>
        <n-button v-if="allowCancel" @click="props.closeModal">
          Cancel
        </n-button>
        <n-button v-if="allowLogout" @click="logout()">
          Logout
        </n-button>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped>
</style>
