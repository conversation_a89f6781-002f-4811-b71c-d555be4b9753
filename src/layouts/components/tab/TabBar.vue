<script setup lang="ts">
import { onBeforeMount, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import moment from 'moment'
import { useAppStore } from '@/store'
import { useEventStore } from '@/store/event'
import { renderIcon } from '@/utils'
import TableModal from '@/views/event/components/TableModal.vue'
import { useBoolean } from '@/hooks'
import ConfirmModal from '@/views/ticket/ConfirmModal.vue'

// stores & hooks
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const eventStore = useEventStore()

// visibility states
const { bool: drawerVisible, setTrue: openDrawer, setFalse: closeDrawer } = useBoolean(false)
const { bool: formModalVisible, setTrue: openFormModal } = useBoolean(false)
const { bool: confirmModalVisible, setTrue: openConfirmModal, setFalse: closeConfirmModal } = useBoolean(false)

// etc
const selectedEventId = ref(0)
const modalType = ref<ModalType>('add')
const options = [
  {
    label: 'Edit',
    key: 'edit',
    icon: renderIcon('icon-park-outline:edit-two'),
  },
  {
    label: 'Delete',
    key: 'delete',
    icon: renderIcon('icon-park-outline:delete'),
  },
]

async function fetchEventList(reset: boolean = false) {
  await eventStore.fetchEventList(reset)
}

async function handleCreate(data: Entity.Event.CreateParams) {
  await eventStore.createEvent(data)
  eventStore.handleResetSearch()
  await fetchEventList(true)
  window.$message.success('Event created successfully!')
}

async function handleUpdate(data: Entity.Event.UpdateParams) {
  await eventStore.updateEvent(data)
  eventStore.handleResetSearch()
  await fetchEventList(true)
  window.$message.success('Event updated successfully!')
  eventStore.selectedEvent = eventStore.eventList.find(event => event.id === data.id)
}

async function handleDelete() {
  await eventStore.deleteEvent(selectedEventId.value)
  eventStore.handleResetSearch()
  await fetchEventList(true)
  window.$message.success('Event deleted successfully!')
  reloadPageWithNotify()
}

function handleEventChange(id) {
  eventStore.selectedEvent = eventStore.eventList.find(event => event.id === id)
  selectedEventId.value = id
  localStorage.setItem('selectedEventId', id)
  closeDrawer()
}

function handleOpenConfirmModal(id: number) {
  selectedEventId.value = id
  openConfirmModal()
}

function handleSelect(key: string | number, item: Entity.Event) {
  if (key === 'delete' && item) {
    handleOpenConfirmModal(item.id)
  }

  if (key === 'edit' && item) {
    handleEdit(item)
  }
}

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleEdit(row: Entity.Event) {
  eventStore.editData = row
  setModalType('edit')
  openFormModal()
}

function handleAddTable() {
  setModalType('add')
  openFormModal()
}

function reloadPageWithNotify() {
  let countdown = 3
  const intervalId = setInterval(() => {
    window.$message.info(`Reloading in ${countdown} second${countdown !== 1 ? 's' : ''}`)
    countdown--

    if (countdown < 0) {
      clearInterval(intervalId)
      window.location.reload()
    }
  }, 1000)
}

watch(() => eventStore.selectedEvent, () => {
  appStore.reloadPage()
})

onBeforeMount(() => {
  if (localStorage.getItem('companyId') === '0') {
    router.push('/select-company')
  }
})

onMounted(async () => {
  await fetchEventList(true)

  if (eventStore.eventList.length < 1) {
    router.push('/event-stepper')
  }

  if (localStorage.getItem('selectedEventId') || route.query.eventId) {
    const eventId = localStorage.getItem('selectedEventId') || route.query.eventId
    eventStore.selectedEvent = eventStore.eventList.find(item => item.id === Number(eventId))
  }

  if (!eventStore.selectedEvent) {
    eventStore.selectedEvent = eventStore.eventList[0]
  }
})
</script>

<template>
  <n-card content-style="padding-left:0 ;padding-right:0;" class="rounded-unset mb-8" :bordered="false" vertical footer-class="" footer-style="padding:14px 24px;">
    <div v-if="eventStore.selectedEvent">
      <div class="flex sm:flex-row flex-col justify-between sm:items-center gap-4">
        <n-ellipsis :line-clamp="1">
          <h1 class="fw-600 text-3xl">
            {{ eventStore.selectedEvent?.name }}
          </h1>
        </n-ellipsis>

        <NButton class="w-auto shadow border-0 bg-white" type="default" :bordered="false" icon-placement="right" @click="openDrawer">
          Switch Event
          <template #icon>
            <icon-park-outline-switch />
          </template>
        </NButton>
      </div>
    </div>
    <div v-else class="w-100%">
      <n-skeleton text :repeat="1" />
      <n-skeleton text style="width: 100%" />
    </div>

    <n-drawer
      v-model:show="drawerVisible"
      title="Choose Your Event"
      placement="right"
      :show-header="true"
      close-on-esc
      style="border-top-left-radius: 20px; border-bottom-left-radius: 20px;"
      class="!max-w-85% !w-370px !sm:w-500px"
    >
      <n-drawer-content>
        <div class="flex flex-col sm:flex-row justify-between sm:items-center mt-4 mb-8 gap-4">
          <div class="text-2xl fw-bold">
            Select Your Event
          </div>
          <NButton type="default" @click="handleAddTable">
            <template #icon>
              <icon-park-outline-add-one />
            </template>
            {{ $t('common.addNew') }}
          </NButton>
        </div>

        <div v-for="item in eventStore.eventList" :key="item.id" class="bg-[#E0EBE2] px-4 py-3 mb-4 rounded-xl hover:cursor-pointer" @click="handleEventChange(item.id)">
          <div class="flex justify-between">
            <span class="text-base fw-semibold">{{ item.name }}</span>
            <n-dropdown
              placement="bottom-start"
              trigger="click"
              size="medium"
              :options="options"
              @select="(key) => handleSelect(key, item)"
            >
              <NButton secondary class="bg-transparent" @click.stop>
                <icon-park-outline-more-one />
              </NButton>
            </n-dropdown>
          </div>

          <div class="mt-2">
            <div class="flex gap-12">
              <div class="flex flex-col">
                <div class="text-theme">
                  Start At
                </div>
                <div class="text-theme fw-semibold">
                  {{ moment(item.startAt).format('YYYY MMM DD h:mm a') }}
                </div>
              </div>

              <div class="flex flex-col">
                <div class="text-theme">
                  End At
                </div>
                <div class="text-theme fw-semibold">
                  {{ moment(item.endAt).format('YYYY MMM DD h:mm a') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>

    <ConfirmModal
      :show-modal="confirmModalVisible"
      title="Confirm Delete"
      message="Are you sure you want to delete this event?"
      confirm-button-text="Yes, Delete!"
      cancel-button-text="Cancel"
      @confirm="handleDelete"
      @cancel="closeConfirmModal"
      @update:show-modal="val => confirmModalVisible = val"
    />

    <TableModal
      v-model:visible="formModalVisible"
      :type="modalType"
      :modal-data="eventStore.editData"
      :company-id="Number(eventStore.companyId)"
      :on-create="handleCreate"
      :on-update="handleUpdate"
    />
  </n-card>
</template>
