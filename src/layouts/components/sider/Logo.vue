<script setup lang="ts">
import { useAppStore } from '@/store'
import Logo from '@/assets/white-logo.png'
import Lime from '@/assets/lime.svg'

const router = useRouter()
const appStore = useAppStore()
</script>

<template>
  <div
    class="h-60px text-xl flex-center cursor-pointer gap-2 p-x-2 mt-5"
    @click="router.push('/')"
  >
    <img v-if="appStore.collapsed" :src="Lime" alt="" class="w-70%">
    <img v-else :src="Logo" alt="" class="w-70%">
  </div>
</template>

<style scoped></style>
