export const staticRoutes: AppRoute.RowRoute[] = [
  {
    name: 'dashboard',
    path: '/dashboard',
    title: 'Dashboard',
    requiresAuth: true,
    icon: 'icon-park-outline:analysis',
    menuType: 'page',
    componentPath: '/dashboard/monitor/index.vue',
    pinTab: true,
    id: 1,
    pid: null,
  },
  {
    name: 'selectCompany',
    path: '/select-company',
    title: 'Select Company',
    requiresAuth: true,
    icon: 'icon-park-outline:building-one',
    componentPath: '/company/SelectCompany.vue',
    id: 2,
    pid: null,
    hide: true,
  },
  {
    name: 'event',
    path: '/events',
    title: 'Event',
    requiresAuth: true,
    icon: 'icon-park-outline:calendar',
    componentPath: '/event/index.vue',
    id: 3,
    pid: null,
    hide: true,
  },
  {
    name: 'eventStepper',
    path: '/event-stepper',
    title: 'Event Stepper',
    requiresAuth: true,
    componentPath: '/event/Stepper.vue',
    id: 4,
    pid: null,
    hide: true,
  },
  {
    name: 'subEvent',
    path: '/event-details',
    title: 'Sub Event',
    requiresAuth: true,
    icon: 'icon-park-outline:calendar',
    componentPath: '/eventDetail/index.vue',
    id: 5,
    pid: null,
  },
  {
    name: 'hallLayout',
    path: '/hall-layout',
    title: 'Hall Layout',
    requiresAuth: true,
    icon: 'icon-park-outline:calendar',
    componentPath: '/eventDetail/components/HallLayout.vue',
    id: 6,
    pid: null,
    hide: true,
  },
  // {
  //   name: 'test',
  //   path: '/test',
  //   title: 'Test',
  //   requiresAuth: true,
  //   icon: 'icon-park-outline:calendar',
  //   componentPath: '/test/test.vue',
  //   id: 6,
  //   pid: null,
  //   hide: true,
  // },
  {
    name: 'approval',
    path: '/approvals',
    title: 'Approval',
    requiresAuth: true,
    icon: 'mdi:user-multiple-tick-outline',
    componentPath: '/ticketRegistrationApproval/index.vue',
    id: 8,
    pid: null,
  },
  {
    name: 'ticket',
    path: '/tickets',
    title: 'Ticket',
    requiresAuth: true,
    icon: 'icon-park-outline:ticket',
    componentPath: '/ticket/index.vue',
    id: 7,
    pid: null,
  },
  {
    name: 'emailCampaign',
    path: '/email-campaigns',
    title: 'Email Campaign',
    requiresAuth: true,
    icon: 'icon-park-outline:send-email',
    componentPath: '/emailCampaign/index.vue',
    id: 7,
    pid: null,
  },
  {
    name: 'luckyDraw',
    path: '/lucky-draws',
    title: 'Lucky Draw',
    requiresAuth: true,
    icon: 'iconoir:gift',
    componentPath: '/luckyDraw/index.vue',
    id: 8,
    pid: null,
    hide: true,
  },
  {
    name: 'drawResult',
    path: '/draw-results',
    title: 'Draw Result',
    requiresAuth: true,
    componentPath: '/luckyDraw/DrawResult.vue',
    id: 9,
    pid: null,
    hide: true,
  },
  {
    name: 'checkIn',
    path: '/check-ins',
    title: 'Check-In',
    requiresAuth: true,
    componentPath: '/checkIn/index.vue',
    id: 10,
    pid: null,
    hide: true,
  },
  {
    name: 'checkInResult',
    path: '/check-in-results',
    title: 'Check-In Result',
    requiresAuth: true,
    componentPath: '/checkIn/CheckInResult.vue',
    id: 11,
    pid: null,
    hide: true,
  },
  {
    name: 'ticketPreview',
    path: '/ticket-preview',
    title: 'Ticket Preview',
    requiresAuth: true,
    componentPath: '/ticket/Preview.vue',
    id: 12,
    pid: null,
    hide: true,
  },
  {
    name: 'vendorManagement',
    path: '#',
    title: 'Vendor Management',
    requiresAuth: true,
    icon: 'icon-park-outline:data-user',
    id: 13,
    pid: null,
  },
  {
    name: 'vendor',
    path: '/vendors',
    title: 'Vendor',
    requiresAuth: true,
    componentPath: '/vendor/index.vue',
    id: 14,
    pid: 13,
  },
  {
    name: 'vendorPackage',
    path: '/vendor-packages',
    title: 'Vendor Package',
    requiresAuth: true,
    componentPath: '/vendor/package/index.vue',
    id: 15,
    pid: 13,
  },
  {
    name: 'license',
    path: '/license',
    title: 'License',
    requiresAuth: true,
    componentPath: '/license/index.vue',
    id: 16,
    pid: null,
    hide: true,
  },
  // {
  //   name: 'about',
  //   path: '/about',
  //   title: '关于',
  //   requiresAuth: true,
  //   icon: 'icon-park-outline:info',
  //   componentPath: '/about/index.vue',
  //   id: 40,
  //   pid: null,
  // },
]
