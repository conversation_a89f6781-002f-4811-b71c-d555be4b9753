<script setup lang="ts">
import { defineProps, ref } from 'vue'
import type { MenuOption } from 'naive-ui'
import { useAppStore } from '@/store' // Adjust the import path according to your project structure

// Define props
const props = defineProps({
  initialTab: {
    type: String,
    default: '',
  },
  menuItems: {
    type: Array as () => MenuOption[],
    default: () => [],
  },
  components: {
    type: Object as () => Record<string, any>,
    default: () => ({}),
  },
})

const activeTab = ref<string>(props.initialTab)
const appStore = useAppStore()

function handleMenuClick(key: string) {
  activeTab.value = key
}
</script>

<template>
  <n-flex vertical>
    <n-space>
      <n-card class="min-w-60 w-auto shadow mb-4" content-style="padding:12px;">
        <n-menu
          mode="horizontal"
          :options="props.menuItems"
          :indent="14"
          :value="activeTab"
          style="font-size:0.85rem"
          @update:value="handleMenuClick"
        />
      </n-card>
    </n-space>

    <transition :name="appStore.transitionAnimation" mode="out-in">
      <component :is="props.components[activeTab]" :key="activeTab" />
    </transition>
  </n-flex>
</template>

<style scoped></style>
