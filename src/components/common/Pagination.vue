<script setup lang="ts">
const props = defineProps({
  count: {
    type: Number,
    default: 0,
  },
  page: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
})

const emit = defineEmits<{
  change: [page: number, pageSize: number]
}>()

const displayOrder: Array<'pages' | 'size-picker' | 'quick-jumper'> = ['size-picker', 'pages']

function changePage(newPage: number) {
  emit('change', newPage, props.pageSize)
}

function changePageSize(newPageSize: number) {
  emit('change', props.page, newPageSize)
}
</script>

<template>
  <n-pagination
    v-if="props.count > 0"
    :page="props.page"
    :page-size="props.pageSize"
    :page-sizes="[10, 15, 20, 25]"
    :item-count="props.count"
    :display-order="displayOrder"
    show-size-picker
    @update:page="changePage"
    @update:page-size="changePageSize"
  />
</template>

<style scoped></style>
