<script setup lang="ts">
interface Props {
  showWatermark: boolean
  text?: string
}
const props = withDefaults(defineProps<Props>(), {
  showWatermark: false,
  text: 'Watermark',
})
</script>

<template>
  <n-watermark
    v-if="props.showWatermark"
    :content="props.text"
    cross
    fullscreen
    :font-size="26"
    :line-height="50"
    :width="384"
    :height="384"
    :x-offset="12"
    :y-offset="20"
    :rotate="-15"
  />
</template>
