<script setup lang="ts">
import Lime from '@/assets/lime.svg'
</script>

<template>
  <naive-provider>
    <div id="loading-container">
      <n-image :src="Lime" alt="" width="50" height="50" />
      <div class="loader" />
    </div>
  </naive-provider>
</template>

<style scoped>
  #loading-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 40px;
    position: fixed;
    background-color: aliceblue;
    z-index: 1;
  }

  .loader {
    display: block;
    --height-of-loader: 8px;
    --loader-color: #7f9a66;
    width: 200px;
    height: var(--height-of-loader);
    border-radius: 30px;
    background-color: rgba(0,0,0,0.2);
    position: relative;
  }

  .loader::before {
    content: "";
    position: absolute;
    background: var(--loader-color);
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    border-radius: 30px;
    animation: moving 1s ease-in-out infinite;
    ;
  }

  @keyframes moving {
    50% {
      width: 100%;
    }

    100% {
      width: 0;
      right: 0;
      left: unset;
    }
  }
</style>
