import { ref } from 'vue'
import { defineStore } from 'pinia'
import moment from 'moment-timezone'
import type { UploadFileInfo } from 'naive-ui'
import { handlePageChange } from '@/utils/pagination'
import { create, del, get, list, postPicture, saveHallLayout, update } from '@/service/api/event-detail'
import { useMatchType } from '@/hooks'

const defaultFormModel: Nullable<Entity.EventDetail.CreateParams> = {
  hallLayout: null,
  eventId: null,
  checkInStartAt: null,
  checkInEndAt: null,
  name: null,
}

export const useEventDetailStore = defineStore('eventDetailStore', () => {
  const { matchType } = useMatchType()

  const eventDetailList = ref<Entity.EventDetail[]>([])
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const eventId = ref<number>(0)

  const editData = ref<Entity.EventDetail | null>(null)
  const searchKeyword = ref<string | null>(null)

  const formModel = ref<Nullable<Entity.EventDetail.CreateParams>>(defaultFormModel)

  async function fetchEventDetailList(): Promise<boolean> {
    const { success, data } = await list(eventId.value)
    eventDetailList.value = data.list.map(eventDetail => ({
      ...eventDetail,
      startAt: moment(eventDetail.checkInStartAt).local().format('YYYY-MM-DD HH:mm:ss'),
      endAt: moment(eventDetail.checkInEndAt).local().format('YYYY-MM-DD HH:mm:ss'),
    }))
    totalItems.value = data.totalItems
    return success
  }

  async function createEventDetail(params: Entity.EventDetail.CreateParams): Promise<Entity.EventDetail> {
    const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    params.checkInStartAt = moment.tz(params.checkInStartAt, localTimeZone).utc().format()
    params.checkInEndAt = moment.tz(params.checkInEndAt, localTimeZone).utc().format()

    const { data, success } = await create(params)
    if (success) {
      window.$message.success('Event detail created successfully !')
      resetFormModel()
    }
    return data
  }

  async function updateEventDetail(params: Entity.EventDetail.UpdateParams): Promise<boolean> {
    const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    params.checkInStartAt = moment.tz(params.checkInStartAt, localTimeZone).utc().format()
    params.checkInEndAt = moment.tz(params.checkInEndAt, localTimeZone).utc().format()

    const { id, ...others } = params
    const { success } = await update(id, others)
    if (success) {
      window.$message.success('Event detail updated successfully !')
    }
    return success
  }

  async function uploadImage(eventDetailId: number, files: UploadFileInfo[]): Promise<PictureUrl | undefined> {
    const fileInfo = files[0]
    if (!fileInfo.file) {
      return
    }
    const formData = new FormData()
    formData.append('files[]', fileInfo.file)
    const response = await postPicture(eventDetailId, formData)
    if (response && response.data) {
      return response.data
    }
    window.$message.error('Failed to get the picture URL from the response')
  }

  async function getEventDetail(id: number): Promise<Entity.EventDetail> {
    const { data } = await get(id)
    return data
  }

  async function deleteEventDetail(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  async function saveLayout(params: Entity.EventDetail.UpdateLayoutParams) {
    const { success } = await saveHallLayout(params)
    return success
  }

  function resetFormModel() {
    formModel.value = formModel.value = {
      hallLayout: null,
      eventId: null,
      checkInStartAt: null,
      checkInEndAt: null,
      name: null,
    }
  }

  function reset() {
    eventDetailList.value = []
    eventId.value = 0
  }

  return {
    reset,
    editData,
    matchType,
    searchKeyword,
    formModel,
    defaultFormModel,
    eventDetailList,
    totalItems,
    currentPage,
    currentSize,
    fetchEventDetailList,
    handlePageChange: (page: number, size: number) => handlePageChange(currentPage, currentSize, page, size, fetchEventDetailList),
    deleteEventDetail,
    updateEventDetail,
    createEventDetail,
    getEventDetail,
    uploadImage,
    eventId,
    saveLayout,
  }
})
