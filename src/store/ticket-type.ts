import { ref } from 'vue'
import { defineStore } from 'pinia'
import { assignment, create, del, list, update } from '@/service/api/ticket-type'

export const useTicketTypeStore = defineStore('ticketTypeStore', () => {
  function getDefaultFormModel(): Nullable<Entity.TicketType.CreateParams> {
    return {
      name: null,
      eventId: null,
    }
  }

  const ticketTypeList = ref<Entity.TicketType[]>([])
  const ticketAssignment = ref<Entity.TicketType.Assignment[]>([])
  const editData = ref<Entity.TicketType | null>(null)
  const formModel = ref<Nullable<Entity.TicketType.CreateParams>>(getDefaultFormModel())
  const eventId = ref<number>(0)

  async function fetchTicketTypeList(): Promise<boolean> {
    const { success, data } = await list({ eventId: eventId.value })
    ticketTypeList.value = data.list
    return success
  }

  async function createTicketType(params: Entity.TicketType.CreateParams): Promise<boolean> {
    const { success } = await create({ ...params, eventId: eventId.value })
    if (success) {
      formModel.value = getDefaultFormModel()
    }
    return success
  }

  async function updateTicketType(params: Entity.TicketType.UpdateParams): Promise<boolean> {
    const { id, ...others } = params
    const { success } = await update(id, others)
    return success
  }

  async function deleteTicketType(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  async function fetchTicketTypeAssignment(): Promise<boolean> {
    const { success, data } = await assignment({ eventId: eventId.value })
    ticketAssignment.value = data
    return success
  }

  function reset() {
    ticketTypeList.value = []
    eventId.value = 0
  }

  return {
    reset,
    eventId,
    editData,
    formModel,
    getDefaultFormModel,
    ticketTypeList,
    fetchTicketTypeList,
    deleteTicketType,
    updateTicketType,
    createTicketType,
    fetchTicketTypeAssignment,
    ticketAssignment,
  }
})
