import { ref } from 'vue'
import { defineStore } from 'pinia'
import { handlePageChange } from '@/utils/pagination'
import { create, del, list, update } from '@/service/api/company'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'

const defaultSearchModel: Criteria<Entity.Company.SearchColumns>[] = defaultCriteriaValue()

const defaultFormModel: Nullable<Entity.Company.CreateParams> = {
  name: null,
  email: null,
  addressLine1: null,
  addressLine2: null,
  city: null,
  state: null,
  postalCode: null,
  countryId: null,
  callingCodeId: null,
  phone: null,
}

export const useCompanyStore = defineStore('companyStore', () => {
  const { matchType } = useMatchType()
  const companyList = ref<Entity.Company[]>([])
  const totalItems = ref<number>(0)
  const searchModel = ref<Criteria<Entity.Company.SearchColumns>[]>(defaultCriteriaValue())
  const searchKeyword = ref<string | null>(null)
  const companyId = ref<number>(0)

  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const editData = ref<Entity.Company | null>(null)
  const formModel = ref<Nullable<Entity.Company.CreateParams>>(defaultFormModel)

  async function fetchCompanyList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    // For keyword Search
    const columns: Entity.Company.SearchColumns[] = []
    // For advanced Filter Search
    const { success, data } = await list<ListParam<Entity.Company.SearchColumns>>({ criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, columns, matchType: matchType.value })
    companyList.value = data.list
    totalItems.value = data.totalItems
    return success
  }

  async function createCompany(params: Entity.Company.CreateParams): Promise<boolean> {
    const { success } = await create(params)
    if (success) {
      resetFormModel()
    }
    return success
  }

  async function updateCompany(params: Entity.Company.UpdateParams): Promise<boolean> {
    const { id, ...others } = params
    const { success } = await update(id, others)
    return success
  }

  async function deleteCompany(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function resetFormModel() {
    formModel.value = {
      name: null,
      email: null,
      addressLine1: null,
      addressLine2: null,
      city: null,
      state: null,
      postalCode: null,
      countryId: null,
      callingCodeId: null,
      phone: null,
    }
  }

  function reset() {
    companyList.value = []
    formModel.value = defaultFormModel
    searchKeyword.value = null
    editData.value = null
  }

  async function setCompanyIdByLocal() {
    const storedCompanyId = localStorage.getItem('companyId')

    if (storedCompanyId !== null) {
      companyId.value = Number(storedCompanyId)
    }
    else {
      companyId.value = 0
    }
  }

  return {
    setCompanyIdByLocal,
    companyId,
    reset,
    resetFormModel,
    editData,
    searchModel,
    searchKeyword,
    matchType,
    formModel,
    defaultFormModel,
    companyList,
    totalItems,
    currentPage,
    currentSize,
    fetchCompanyList,
    handleResetSearch,
    handlePageChange: (page: number, size: number) => handlePageChange(currentPage, currentSize, page, size, fetchCompanyList),
    deleteCompany,
    updateCompany,
    createCompany,

  }
})
