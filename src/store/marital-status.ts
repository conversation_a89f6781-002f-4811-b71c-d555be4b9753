import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/service/axios/public'

export const useMaritalStatusStore = defineStore('maritalStatusStore', () => {
  const maritalStatuses = ref<Entity.MaritalStatus[] | null>(null)
  const lastFetched = ref<number | null>(null)
  const cacheDuration = 30 * 60 * 1000 // 30 minutes

  async function maritalStatusList(): Promise<Entity.MaritalStatus[] | null> {
    const now = Date.now()
    const cachedData = localStorage.getItem('maritalStatuses')
    const cachedTime = localStorage.getItem('maritalStatusesLastFetched')

    if (cachedData && cachedTime && (now - Number(cachedTime) < cacheDuration)) {
      maritalStatuses.value = JSON.parse(cachedData)
      lastFetched.value = Number(cachedTime)
      return maritalStatuses.value
    }

    const response = await api.get<ListResponse<Entity.MaritalStatus>>('/marital-statuses')
    maritalStatuses.value = response.data.list
    lastFetched.value = now

    localStorage.setItem('maritalStatuses', JSON.stringify(maritalStatuses.value))
    localStorage.setItem('maritalStatusesLastFetched', String(lastFetched.value))

    return maritalStatuses.value
  }

  function getMaritalStatusById(maritalStatusId: number): Entity.MaritalStatus | null {
    return maritalStatuses.value
      ? maritalStatuses.value.find(c => c.id === maritalStatusId) || null
      : null
  }

  const maritalStatusOptions = computed(() =>
    (maritalStatuses.value?.map(maritalStatus => ({
      label: `${maritalStatus.name}`,
      value: maritalStatus.id,
    }))) || [],
  )

  function reset() {
    maritalStatuses.value = []
  }

  return {
    reset,
    maritalStatuses,
    lastFetched,
    cacheDuration,
    maritalStatusList,
    getMaritalStatusById,
    maritalStatusOptions,
  }
})
