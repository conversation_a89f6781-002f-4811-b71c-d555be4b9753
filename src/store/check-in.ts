import { ref } from 'vue'
import { defineStore } from 'pinia'
import { handlePageChange } from '@/utils/pagination'
import { create, del, exports, list } from '@/service/api/check-in'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'

const defaultSearchModel: Criteria<Entity.CheckIn.SearchColumns>[] = defaultCriteriaValue()

const defaultFormModel: Nullable<Entity.CheckIn.CreateParams> = {
  ticketNumber: '',
  eventDetailId: null,
}

export const useCheckInStore = defineStore('checkInStore', () => {
  const route = useRoute()
  const { matchType } = useMatchType()

  const checkInList = ref<Entity.CheckIn[]>([])
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const eventDetailId = ref<number>(Number(route.query.eventDetailId))

  const editData = ref<Entity.CheckIn | null>(null)
  const searchKeyword = ref<string | null>(null)

  const searchModel = ref<Criteria<Entity.CheckIn.SearchColumns>[]>(defaultSearchModel)
  const formModel = ref<Nullable<Entity.CheckIn.CreateParams>>(defaultFormModel)

  async function fetchCheckInList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    // For keyword Search
    const columns: Entity.CheckIn.SearchColumns[] = []

    // For advanced Filter Search
    const { success, data } = await list<ListParam<Entity.CheckIn.SearchColumns>>({ criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, columns, matchType: matchType.value }, eventDetailId.value)
    checkInList.value = data.list
    totalItems.value = data.totalItems
    return success
  }

  async function createCheckIn(params: Entity.CheckIn.CreateParams): Promise<Entity.Ticket> {
    const { data } = await create(params)
    return data
  }

  async function deleteCheckIn(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  async function exportTicket(ids: Array<string | number> = [], isCheckedIn: boolean = true): Promise<string> {
    const { data } = await exports({ eventDetailId: eventDetailId.value, moduleIds: ids, isCheckedIn })
    return data.csvFile
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function resetFormModel() {
    formModel.value = {
      ticketNumber: '',
      eventDetailId: null,
    }
  }

  function reset() {
    checkInList.value = []
    eventDetailId.value = 0
    editData.value = null
    searchKeyword.value = null
  }

  return {
    reset,
    resetFormModel,
    editData,
    matchType,
    searchModel,
    searchKeyword,
    formModel,
    defaultFormModel,
    checkInList,
    totalItems,
    currentPage,
    currentSize,
    fetchCheckInList,
    handleResetSearch,
    handlePageChange: (page: number, size: number) => handlePageChange(currentPage, currentSize, page, size, fetchCheckInList),
    deleteCheckIn,
    createCheckIn,
    eventDetailId,
    exportTicket,
  }
})
