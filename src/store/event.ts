import { ref } from 'vue'
import { defineStore } from 'pinia'
import moment from 'moment-timezone'
import { handlePageChange } from '@/utils/pagination'
import { create, del, list, update } from '@/service/api/event'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'

const defaultSearchModel: Criteria<Entity.Event.SearchColumns>[] = defaultCriteriaValue()

const defaultFormModel: Nullable<Entity.Event.CreateParams> = {
  name: null,
  description: null,
  startAt: null,
  endAt: null,
}

export const useEventStore = defineStore('eventStore', () => {
  const { matchType } = useMatchType()

  const eventList = ref<Entity.Event[]>([])
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const companyId = ref<any>(localStorage.getItem('companyId'))
  const selectedEvent = ref<Entity.Event>()

  const editData = ref<Entity.Event | null>(null)
  const searchKeyword = ref<string | null>(null)

  const searchModel = ref<Criteria<Entity.Event.SearchColumns>[]>(defaultSearchModel)
  const formModel = ref<Nullable<Entity.Event.CreateParams>>(defaultFormModel)

  async function fetchEventList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      eventList.value = []
      currentPage.value = 1
      searchModel.value = defaultSearchModel
    }

    if (companyId.value === 0 || Number.isNaN(companyId.value)) {
      return false
    }

    const { success, data } = await list<ListParam<Entity.Event.SearchColumns>>(companyId.value)
    eventList.value = data.list.map(event => ({
      ...event,
      startAt: moment(event.startAt).local().format('YYYY-MM-DD HH:mm:ss'),
      endAt: moment(event.endAt).local().format('YYYY-MM-DD HH:mm:ss'),
    }))
    totalItems.value = data.totalItems
    return success
  }

  async function createEvent(params: Entity.Event.CreateParams): Promise<boolean> {
    const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    params.startAt = moment.tz(params.startAt, localTimeZone).utc().format()
    params.endAt = moment.tz(params.endAt, localTimeZone).utc().format()

    const { success } = await create(params)
    if (success) {
      resetFormModel()
    }
    return success
  }

  async function updateEvent(params: Entity.Event.UpdateParams): Promise<boolean> {
    const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    params.startAt = moment.tz(params.startAt, localTimeZone).utc().format()
    params.endAt = moment.tz(params.endAt, localTimeZone).utc().format()

    const { id, ...others } = params
    const { success } = await update(id, others)
    return success
  }

  async function deleteEvent(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function resetFormModel() {
    formModel.value = {
      name: null,
      description: null,
      startAt: null,
      endAt: null,
    }
  }

  function reset() {
    companyId.value = 0
    eventList.value = []
  }

  return {
    reset,
    resetFormModel,
    selectedEvent,
    companyId,
    editData,
    matchType,
    searchModel,
    searchKeyword,
    formModel,
    defaultFormModel,
    eventList,
    totalItems,
    currentPage,
    currentSize,
    fetchEventList,
    handleResetSearch,
    handlePageChange: (page: number, size: number) => handlePageChange(currentPage, currentSize, page, size, fetchEventList),
    deleteEvent,
    updateEvent,
    createEvent,
  }
})
