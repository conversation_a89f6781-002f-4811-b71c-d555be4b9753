import { ref } from 'vue'
import { defineStore } from 'pinia'
import { handlePageChange } from '@/utils/pagination'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'
import { create, del, list, update } from '@/service/api/sponsor-package'

export const useSponsorPackageStore = defineStore('sponsorPackage', () => {
  const { matchType } = useMatchType()

  const eventId = ref<number>(0)
  const sponsorPackageList = ref<Entity.SponsorPackage[]>([])
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)

  const editData = ref<Entity.SponsorPackage | null>(null)
  const searchKeyword = ref<string | null>(null)

  const defaultSearchModel: Criteria<Entity.SponsorPackage.SearchColumns>[] = defaultCriteriaValue()

  const defaultFormModel: Nullable<Entity.SponsorPackage.CreateParams> = {
    eventId: null,
    name: null,
    amountSponsored: null,
    customNote: null,
    sortOrder: null,
    speakingSlotDuration: null,
    totalBooths: null,
    totalTables: null,
    totalTickets: null,
  }

  const searchModel = ref<Criteria<Entity.SponsorPackage.SearchColumns>[]>(defaultSearchModel)
  const formModel = ref<Nullable<Entity.SponsorPackage.CreateParams>>(defaultFormModel)

  async function fetchSponsorPackageList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    // For keyword Search
    const columns: Entity.SponsorPackage.SearchColumns[] = ['name']

    // For advanced Filter Search
    const { success, data } = await list<ListParam<Entity.SponsorPackage.SearchColumns>>(eventId.value, { criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, columns, matchType: matchType.value })
    totalItems.value = data.totalItems
    sponsorPackageList.value = data.list
    return success
  }

  async function createSponsorPackage(params: Entity.SponsorPackage.CreateParams): Promise<boolean> {
    params.eventId = eventId.value
    const { success } = await create(params)
    return success
  }

  async function updateSponsorPackage(params: Entity.SponsorPackage.UpdateParams): Promise<boolean> {
    const { id, ...others } = params
    const { success } = await update(id, others)
    return success
  }

  async function deleteSponsorPackage(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function resetFormModel() {
    formModel.value = {
      eventId: null,
      name: null,
      amountSponsored: null,
      customNote: null,
      sortOrder: null,
      speakingSlotDuration: null,
      totalBooths: null,
      totalTables: null,
      totalTickets: null,
    }
  }

  function reset() {
    eventId.value = 0
    sponsorPackageList.value = []
  }

  return {
    reset,
    resetFormModel,
    eventId,
    editData,
    matchType,
    searchModel,
    searchKeyword,
    formModel,
    defaultFormModel,
    sponsorPackageList,
    totalItems,
    currentPage,
    currentSize,
    fetchSponsorPackageList,
    handleResetSearch,
    handlePageChange: (page: number, size: number) => handlePageChange(currentPage, currentSize, page, size, fetchSponsorPackageList),
    deleteSponsorPackage,
    updateSponsorPackage,
    createSponsorPackage,
  }
})
