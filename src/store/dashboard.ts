import { defineStore } from 'pinia'
import { checkInSummary, dashboard } from '@/service/api/dashboard'

export const useDashboardStore = defineStore('dashboardStore', () => {
  async function fetchDashboardSummary(eventId: number): Promise<Entity.Summary> {
    const { data } = await dashboard(eventId)
    return data
  }

  async function fetchCheckInSummary(eventDetailId: number): Promise<Entity.CheckIn.Summary> {
    const { data } = await checkInSummary(eventDetailId)
    return data
  }

  // handleLogout required
  function reset() {
    return true
  }

  return {
    reset,
    fetchCheckInSummary,
    fetchDashboardSummary,
  }
})
