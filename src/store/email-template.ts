import { ref } from 'vue'
import { defineStore } from 'pinia'
import { useBoolean, useMatchType } from '@/hooks'
import { useEmailTemplateApi } from '@/service/api/email-template'

export const useEmailTemplateStore = defineStore('emailTemplateStore', () => {
  const { matchType } = useMatchType()
  const emailTemplateApi = useEmailTemplateApi()

  const formType = ref<ModalType>()
  const { bool: formModalVisible, setTrue: openFormModal, setFalse: closeFormModal } = useBoolean()

  const dataList = ref<Entity.EmailTemplate[]>([])
  const searchKeyword = ref<string | null>(null)
  const eventId = ref<number>(0)

  const editData = ref<Entity.EmailTemplate>()

  async function getList(): Promise<boolean> {
    const { success, data } = await emailTemplateApi.list()
    dataList.value = data.list
    return success
  }

  async function get(): Promise<boolean> {
    const { success, data } = await emailTemplateApi.get(eventId.value)
    dataList.value = data.list
    return success
  }

  return {
    formModalVisible,
    openFormModal,
    closeFormModal,

    formType,

    eventId,
    editData,
    searchKeyword,
    matchType,
    dataList,
    getList,
    get,
  }
})
