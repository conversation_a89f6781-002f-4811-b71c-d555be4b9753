// stores/useResultWindowStore.ts
import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useResultWindowStore = defineStore('resultWindow', () => {
  const resultWindow = ref<Window | null>(null)

  function setResultWindow(win: Window) {
    resultWindow.value = win
  }

  function isWindowOpen() {
    return resultWindow.value && !resultWindow.value.closed
  }

  function postToResultPage(data: any) {
    if (isWindowOpen()) {
      resultWindow.value!.postMessage(JSON.stringify(data), '*')
    }
  }

  return { resultWindow, setResultWindow, isWindowOpen, postToResultPage }
})
