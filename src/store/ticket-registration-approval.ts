import { ref } from 'vue'
import { defineStore } from 'pinia'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'
import { approve, del, exports, list, reject } from '@/service/api/ticket-registration-approval'

export const useTicketRegistrationApprovalStore = defineStore('ticketRegistrationApprovalStore', () => {
  const { matchType } = useMatchType()

  const eventId = ref<number>(0)
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const ticketIds = ref<number[]>([])
  const ticketTypeId = ref<number | null>(null)
  const eventDetailId = ref<number>(0)
  const sort = ref<Sort[]>([])
  const ticketRegistrationApprovalList = ref<Entity.TicketRegistrationApproval[]>([])

  const editData = ref<Entity.TicketRegistrationApproval | null>(null)
  const searchKeyword = ref<string | null>(null)

  const defaultSearchModel: Criteria<Entity.TicketRegistrationApproval.SearchColumns>[] = defaultCriteriaValue()

  const searchModel = ref<Criteria<Entity.TicketRegistrationApproval.SearchColumns>[]>(defaultSearchModel)

  async function fetchTicketRegistrationApprovalList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    const { success, data } = await list<ListParam<Entity.TicketRegistrationApproval.SearchColumns>>(eventId.value, { criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, matchType: matchType.value, sort: sort.value })
    totalItems.value = data.totalItems
    ticketRegistrationApprovalList.value = data.list
    return success
  }

  async function approveTicketRegistration(ticketIds: Array<any> = []): Promise<boolean> {
    const { success } = await approve({ eventId: eventId.value, approvalIds: ticketIds, ticketTypeId: ticketTypeId.value })
    return success
  }

  async function rejectTicketRegistration(ticketIds: Array<any> = []): Promise<boolean> {
    const { success } = await reject({ eventId: eventId.value, approvalIds: ticketIds })
    return success
  }

  async function deleteTicketRegistration(ticketIds: Array<any> = []): Promise<boolean> {
    const { success } = await del(ticketIds)
    return success
  }

  async function exportRegistrationApproval(ids: Array<string | number> = []): Promise<string> {
    const { data } = await exports({ eventId: eventId.value, moduleIds: ids })
    return data.csvFile
  }

  async function handlePageChange(page: number, size: number) {
    currentPage.value = page
    currentSize.value = size
    await fetchTicketRegistrationApprovalList()
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function reset() {
    eventId.value = 0
    ticketRegistrationApprovalList.value = []
  }

  return {
    reset,
    eventId,
    ticketIds,
    eventDetailId,
    ticketTypeId,
    editData,
    matchType,
    searchModel,
    searchKeyword,
    ticketRegistrationApprovalList,
    totalItems,
    currentPage,
    currentSize,
    sort,
    fetchTicketRegistrationApprovalList,
    exportRegistrationApproval,
    handleResetSearch,
    handlePageChange,
    approveTicketRegistration,
    rejectTicketRegistration,
    deleteTicketRegistration,
  }
})
