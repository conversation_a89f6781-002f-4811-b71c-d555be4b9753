import { ref } from 'vue'
import { defineStore } from 'pinia'
import { handlePageChange } from '@/utils/pagination'
import { create, del, getPossibleLuckyDrawWinners, list, update, winners } from '@/service/api/lucky-draw'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'

export const useLuckyDrawStore = defineStore('luckyDrawStore', () => {
  const { matchType } = useMatchType()

  const luckyDrawList = ref<Entity.LuckyDraw[]>([])
  const totalItems = ref<number>(0)
  const searchKeyword = ref<string | null>(null)
  const eventDetailId = ref<number>(0)
  const newWinners = ref<Entity.Ticket[]>([])

  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const editData = ref<Entity.LuckyDraw | null>(null)

  const defaultSearchModel: Criteria<Entity.LuckyDraw.SearchColumns>[] = defaultCriteriaValue()

  const defaultFormModel: Nullable<Entity.LuckyDraw.CreateParams> = {
    name: null,
    description: null,
    eventDetailId: null,
    winnerQuantity: null,
    isRepeatable: false,
    ticketTypeIds: [],
  }

  const formModel = ref<Nullable<Entity.LuckyDraw.CreateParams>>(defaultFormModel)
  const searchModel = ref<Criteria<Entity.LuckyDraw.SearchColumns>[]>(defaultSearchModel)

  async function fetchLuckyDrawList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    // For keyword Search
    const columns: Entity.LuckyDraw.SearchColumns[] = []
    // For advanced Filter Search
    const { success, data } = await list<ListParam<Entity.LuckyDraw.SearchColumns>>({ criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, columns, matchType: matchType.value, eventDetailId: eventDetailId.value })
    luckyDrawList.value = data.list
    totalItems.value = data.totalItems
    return success
  }

  async function createLuckyDraw(params: Entity.LuckyDraw.CreateParams): Promise<boolean> {
    params.eventDetailId = eventDetailId.value
    const { success } = await create(params)
    if (success) {
      resetFormModel()
    }
    return success
  }

  async function updateLuckyDraw(params: Entity.LuckyDraw.UpdateParams): Promise<boolean> {
    const { id, ...others } = params
    const { success } = await update(id, others)
    return success
  }

  async function deleteLuckyDraw(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  async function drawWinners(params: Entity.LuckyDraw.Winner.CreateParams): Promise<boolean> {
    const { data, success } = await getPossibleLuckyDrawWinners(params)
    newWinners.value = data.list
    return success
  }

  async function getHistoryWinners(params: Entity.LuckyDraw.Winner.HistoryParams): Promise<Entity.LuckyDraw.Winner[]> {
    const { data } = await winners(params)
    return data.list
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function resetFormModel() {
    formModel.value = {
      name: null,
      description: null,
      eventDetailId: null,
      winnerQuantity: null,
      isRepeatable: false,
      ticketTypeIds: [],
    }
  }

  function reset() {
    luckyDrawList.value = []
    eventDetailId.value = 0
    newWinners.value = []
  }

  return {
    reset,
    resetFormModel,
    eventDetailId,
    editData,
    searchModel,
    searchKeyword,
    matchType,
    formModel,
    defaultFormModel,
    luckyDrawList,
    totalItems,
    currentPage,
    currentSize,
    fetchLuckyDrawList,
    handleResetSearch,
    handlePageChange: (page: number, size: number) => handlePageChange(currentPage, currentSize, page, size, fetchLuckyDrawList),
    deleteLuckyDraw,
    updateLuckyDraw,
    createLuckyDraw,
    drawWinners,
    getHistoryWinners,
    newWinners,
  }
})
