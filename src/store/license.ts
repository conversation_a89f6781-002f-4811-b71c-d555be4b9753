import { ref } from 'vue'
import { defineStore } from 'pinia'
import { list } from '@/service/api/license'

export const useLicenseStore = defineStore('licenseStore', () => {
  const licenseList = ref<Entity.License[]>([])
  const companyId = ref<any>(localStorage.getItem('companyId'))

  async function fetchLicense(eventId: number): Promise<boolean> {
    const { success, data } = await list({}, companyId.value, eventId)
    licenseList.value = data.list
    return success
  }

  return {
    companyId,
    licenseList,
    fetchLicense,
  }
})
