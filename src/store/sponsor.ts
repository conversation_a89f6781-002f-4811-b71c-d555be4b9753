import { ref } from 'vue'
import { defineStore } from 'pinia'
import { handlePageChange } from '@/utils/pagination'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'
import { create, del, delFile, exports, files, list, sponsorDashboard, update, upload } from '@/service/api/sponsor'

export const useSponsorStore = defineStore('sponsorStore', () => {
  const { matchType } = useMatchType()

  const eventId = ref<number>(0)
  const sponsorList = ref<Entity.Sponsor[]>([])
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const vendorDashboardData = ref<Entity.Sponsor.VendorDashboard>()

  const editData = ref<Entity.Sponsor | null>(null)
  const searchKeyword = ref<string | null>(null)

  const defaultSearchModel: Criteria<Entity.Sponsor.SearchColumns>[] = defaultCriteriaValue()

  const defaultFormModel: Nullable<Entity.Sponsor.CreateParams> = {
    eventId: null,
    sponsorPackageId: null,
    name: null,
    picName: null,
    picEmail: null,
    picContactNumber: null,
    amountReceived: 0,
    address: null,
    customNote: null,
    picPosition: null,
  }

  const searchModel = ref<Criteria<Entity.Sponsor.SearchColumns>[]>(defaultSearchModel)
  const formModel = ref<Nullable<Entity.Sponsor.CreateParams>>(defaultFormModel)

  async function fetchSponsorList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    // For keyword Search
    const columns: Entity.Sponsor.SearchColumns[] = ['email', 'name', 'picName']

    // For advanced Filter Search
    // const { success, data } = await list<ListParam<Entity.Sponsor.SearchColumns>>(eventId.value, {})
    const { success, data } = await list<ListParam<Entity.Sponsor.SearchColumns>>(eventId.value, { criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, columns, matchType: matchType.value })
    totalItems.value = data.totalItems
    sponsorList.value = data.list
    return success
  }

  async function fetchVendorDashboard(): Promise<boolean> {
    const { success, data } = await sponsorDashboard(eventId.value)
    vendorDashboardData.value = data
    return success
  }

  async function createSponsor(params: Entity.Sponsor.CreateParams): Promise<boolean> {
    params.eventId = eventId.value
    const { success } = await create(params)
    return success
  }

  async function updateSponsor(params: Entity.Sponsor.UpdateParams): Promise<boolean> {
    const { id, ...others } = params
    const { success } = await update(id, others)
    return success
  }

  async function deleteSponsor(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  async function uploadRequest(eventId: number, sponsorId: number, fileName: string, mimeType: string): Promise<any> {
    const { data } = await upload({ eventId, sponsorId, fileName, mimeType })
    return data
  }

  async function deleteFile(sponsorId: number, fileName: string): Promise<any> {
    const { data } = await delFile(sponsorId, { fileName })
    return data
  }

  async function fetchSponsorFiles(sponsorId: number): Promise<File[]> {
    const { data } = await files(sponsorId)
    return data.list
  }

  async function exportSponsors(ids: Array<string | number> = []): Promise<string> {
    const { data } = await exports({ eventId: eventId.value, moduleIds: ids })
    return data.csvFile
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function resetFormModel() {
    formModel.value = {
      eventId: null,
      sponsorPackageId: null,
      name: null,
      picName: null,
      picEmail: null,
      picContactNumber: null,
      amountReceived: null,
      address: null,
      customNote: null,
      picPosition: null,
    }
  }

  function reset() {
    eventId.value = 0
    sponsorList.value = []
  }

  return {
    reset,
    resetFormModel,
    fetchVendorDashboard,
    eventId,
    editData,
    matchType,
    searchModel,
    searchKeyword,
    formModel,
    defaultFormModel,
    sponsorList,
    totalItems,
    currentPage,
    currentSize,
    fetchSponsorList,
    handleResetSearch,
    handlePageChange: (page: number, size: number) => handlePageChange(currentPage, currentSize, page, size, fetchSponsorList),
    deleteSponsor,
    updateSponsor,
    createSponsor,
    uploadRequest,
    fetchSponsorFiles,
    vendorDashboardData,
    deleteFile,
    exportSponsors,
  }
})
