import { ref } from 'vue'
import { defineStore } from 'pinia'
import { checkInTickets, create, del, email, exports, guest, imports, list, listNoPagination, preview, publicPreview, update } from '@/service/api/ticket'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'
import { summary } from '@/service/api/check-in'

export const useTicketStore = defineStore('ticketStore', () => {
  type TableAssign = 'true' | 'false' | 'unset'

  const { matchType } = useMatchType()
  const eventId = ref<number>(0)
  const eventDetailId = ref<number>(0)
  const ticketList = ref<Entity.Ticket[]>([])
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)
  const ticketIds = ref<number[]>([])
  const sort = ref<Sort[]>([])
  const checkInSummary = ref<Entity.Ticket.CheckInSummary[]>([])
  const tableAssign = ref<TableAssign>('unset')
  const template = ref<string | null>()
  const emailCampaignId = ref<number | null>()
  const editData = ref<Entity.Ticket | null>(null)
  const searchKeyword = ref<string | null>(null)

  const defaultSearchModel: Criteria<Entity.Ticket.SearchColumns>[] = defaultCriteriaValue()
  const defaultFormModel: Nullable<Entity.Ticket.CreateParams> = getDefaultFormModel()

  const searchModel = ref<Criteria<Entity.Ticket.SearchColumns>[]>(defaultSearchModel)
  const formModel = ref<Nullable<Entity.Ticket.CreateParams>>(defaultFormModel)

  function getDefaultFormModel(): Nullable<Entity.Ticket.CreateParams> {
    return {
      holderName: null,
      companyName: null,
      email: null,
      eventId: null,
      ticketTypeId: null,
      contactNumber: null,
      customNote: [],
      gender: null,
      maritalStatusId: null,
      age: null,
    }
  }

  async function fetchTicketList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    const { success, data } = await list<ListParam<Entity.Ticket.SearchColumns>>(eventId.value, { criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, matchType: matchType.value, sort: sort.value }, tableAssign.value)
    totalItems.value = data.totalItems
    ticketList.value = data.list
    template.value = data.template
    emailCampaignId.value = data.emailCampaignId
    return success
  }

  async function handlePageChange(page: number, size: number) {
    currentPage.value = page
    currentSize.value = size
    await fetchTicketList(false)
  }

  async function fetchTicketListByTicketIds(): Promise<Entity.Ticket[]> {
    interface NewListParam extends ListParam<Entity.Ticket.SearchColumns> {
      ticketIds?: Array<number>
      tableAssign?: TableAssign
    }
    const { data } = await list<NewListParam>(eventId.value, { criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, matchType: matchType.value, ticketIds: ticketIds.value }, tableAssign.value)
    return data.list
  }

  async function fetchTicketListWithoutPagination(): Promise<Entity.Ticket[]> {
    interface NewListParam extends ListParam<Entity.Ticket.SearchColumns> {
      ticketIds?: Array<number>
      tableAssign?: TableAssign
    }
    const { data } = await listNoPagination<NewListParam>(eventId.value, { criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, matchType: matchType.value }, tableAssign.value)
    return data.list
  }

  async function fetchCheckInTicketList(): Promise<Entity.Ticket[]> {
    const { data } = await checkInTickets(eventId.value, eventDetailId.value, { keyword: searchKeyword.value })
    return data.list
  }

  async function sendTicketEmails(ticketIds: Array<any> = []): Promise<boolean> {
    const { success } = await email({ eventId: eventId.value, ticketIds, emailCampaignId: emailCampaignId.value })
    return success
  }

  async function createTicket(params: Entity.Ticket.CreateParams): Promise<boolean> {
    params.eventId = eventId.value
    const { success } = await create(params)
    return success
  }

  async function createGuestTicket(params: Entity.Ticket.CreateGuestParams, eventDetailId: number): Promise<boolean> {
    params.eventId = eventId.value
    params.eventDetailId = eventDetailId
    const { success } = await guest(params)
    if (success) {
      resetFormModel()
    }
    return success
  }

  async function updateTicket(params: Entity.Ticket.UpdateParams): Promise<boolean> {
    const { id, ...others } = params
    const { success } = await update(id, others)
    return success
  }

  async function deleteTicket(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  async function importTicket(id: number, file: File): Promise<boolean> {
    const { success } = await imports(id, file)
    return success
  }

  async function exportTicket(ids: Array<string | number> = []): Promise<string> {
    const { data } = await exports({ eventId: eventId.value, moduleIds: ids })
    return data.csvFile
  }

  async function ticketPreview(ticketNumber: string): Promise<Preview> {
    const { data } = await preview(ticketNumber)
    return data
  }

  async function publicTicketPreview(ticketNumber: string): Promise<string> {
    const { data } = await publicPreview(ticketNumber)
    return data
  }

  async function fetchTicketCheckInSummary(eventId: number): Promise<boolean> {
    const { data, success } = await summary(eventId)
    checkInSummary.value = data
    return success
  }

  function handleResetSearch() {
    searchModel.value = defaultCriteriaValue()
  }

  function resetFormModel() {
    formModel.value = getDefaultFormModel()
  }

  function reset() {
    eventId.value = 0
    ticketList.value = []
    sort.value = []
  }

  return {
    reset,
    resetFormModel,
    publicTicketPreview,
    eventId,
    eventDetailId,
    ticketIds,
    editData,
    matchType,
    searchModel,
    searchKeyword,
    formModel,
    defaultFormModel,
    ticketList,
    totalItems,
    currentPage,
    currentSize,
    sort,
    tableAssign,
    template,
    fetchTicketList,
    fetchTicketListByTicketIds,
    fetchTicketListWithoutPagination,
    handleResetSearch,
    handlePageChange,
    deleteTicket,
    updateTicket,
    createTicket,
    importTicket,
    exportTicket,
    ticketPreview,
    sendTicketEmails,
    createGuestTicket,
    fetchTicketCheckInSummary,
    fetchCheckInTicketList,
    checkInSummary,
  }
})
