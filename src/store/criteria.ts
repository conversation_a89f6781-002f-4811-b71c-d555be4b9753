const criteriaOptions = [
  { value: 'equal', label: 'Equal' },
  { value: 'contain', label: 'Contain' },
  { value: 'moreThan', label: 'More Than' },
  { value: 'lessThan', label: 'Less Than' },
  { value: 'notEqual', label: 'Not Equal' },
]

export function useCriteriaStore(initialCriteria: any) {
  const criteria = initialCriteria

  function addCriteria() {
    criteria.push({ column: null, type: null, value: null })
  }

  function removeCriteria(index: number) {
    criteria.splice(index, 1)
  }

  function clearAll() {
    criteria.splice(0, criteria.length)
    addCriteria()
  }

  return {
    criteria,
    criteriaOptions,
    addCriteria,
    removeCriteria,
    clearAll,
  }
}
