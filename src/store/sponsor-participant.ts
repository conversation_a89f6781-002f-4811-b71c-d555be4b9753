import { ref } from 'vue'
import { defineStore } from 'pinia'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useMatchType } from '@/hooks'
import { del, list } from '@/service/api/sponsor-participant'

export const useSponsorParticipantStore = defineStore('SponsorParticipant', () => {
  const { matchType } = useMatchType()

  const sponsorId = ref<number>(0)
  const sponsorParticipantList = ref<Entity.SponsorParticipant[]>([])
  const totalItems = ref<number>(0)
  const currentPage = ref<number>(1)
  const currentSize = ref<number>(10)

  const editData = ref<Entity.SponsorParticipant | null>(null)
  const searchKeyword = ref<string | null>(null)

  const defaultSearchModel: Criteria<Entity.SponsorParticipant.SearchColumns>[] = defaultCriteriaValue()

  const searchModel = ref<Criteria<Entity.SponsorParticipant.SearchColumns>[]>(defaultSearchModel)

  async function fetchSponsorParticipantList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      currentPage.value = 1
      searchModel.value = defaultCriteriaValue()
    }

    // For keyword Search
    const columns: Entity.SponsorParticipant.SearchColumns[] = ['ticket.name']

    // For advanced Filter Search
    const { success, data } = await list<ListParam<Entity.SponsorParticipant.SearchColumns>>(sponsorId.value, { criteria: searchModel.value, page: currentPage.value, keyword: searchKeyword.value, pageSize: currentSize.value, columns, matchType: matchType.value })
    totalItems.value = data.totalItems
    sponsorParticipantList.value = data.list
    return success
  }

  async function deleteSponsorParticipant(id: number): Promise<boolean> {
    const { success } = await del(id)
    return success
  }

  async function handlePageChange(page: number, size: number) {
    currentPage.value = page
    currentSize.value = size
    await fetchSponsorParticipantList(false)
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function reset() {
    sponsorId.value = 0
    sponsorParticipantList.value = []
  }

  return {
    reset,
    sponsorId,
    editData,
    matchType,
    searchModel,
    searchKeyword,
    sponsorParticipantList,
    totalItems,
    currentPage,
    currentSize,
    fetchSponsorParticipantList,
    handleResetSearch,
    handlePageChange,
    deleteSponsorParticipant,
  }
})
