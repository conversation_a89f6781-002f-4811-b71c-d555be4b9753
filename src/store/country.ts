import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/service/axios/public'

export const useCountryStore = defineStore('countryStore', () => {
  const countries = ref<Entity.Country[] | null>(null)
  const lastFetched = ref<number | null>(null)
  const cacheDuration = 10 * 60 * 1000 // 10 minutes

  async function countryList(): Promise<Entity.Country[] | null> {
    const now = Date.now()
    const cachedData = localStorage.getItem('countries')
    const cachedTime = localStorage.getItem('countriesLastFetched')

    if (cachedData && cachedTime && (now - Number(cachedTime) < cacheDuration)) {
      countries.value = JSON.parse(cachedData)
      lastFetched.value = Number(cachedTime)
      return countries.value
    }

    const response = await api.get<ListResponse<Entity.Country>>('/countries')
    countries.value = response.data.list
    lastFetched.value = now

    localStorage.setItem('countries', JSON.stringify(countries.value))
    localStorage.setItem('countriesLastFetched', String(lastFetched.value))

    return countries.value
  }

  function getCountryById(countryId: number): Entity.Country | null {
    return countries.value
      ? countries.value.find(c => c.id === countryId) || null
      : null
  }

  const countryOptions = computed(() =>
    (countries.value?.map(country => ({
      label: `${country.name} ${country.callingCode.code}`,
      value: country.id,
    }))) || [],
  )

  function reset() {
    countries.value = []
  }

  return {
    reset,
    countries,
    lastFetched,
    cacheDuration,
    countryList,
    getCountryById,
    countryOptions,
  }
})
