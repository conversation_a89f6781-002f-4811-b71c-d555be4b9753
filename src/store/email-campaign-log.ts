import { ref } from 'vue'
import { defineStore } from 'pinia'
import { useEmailCampaignLogApi } from '@/service/api/email-campaign-log'
import { useBoolean } from '@/hooks'

export const useEmailCampaignLogStore = defineStore('emailCampaignLogStore', () => {
  const emailCampaignLogApi = useEmailCampaignLogApi()

  const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

  const dataList = ref<Entity.EmailCampaignLog[]>([])
  const eventId = ref<number>(0)

  async function getList(param: Entity.EmailCampaignLog.ListParams): Promise<boolean> {
    startLoading()
    const { success, data } = await emailCampaignLogApi.list(param)
    dataList.value = data.list
    endLoading()
    return success
  }

  async function download(params: Entity.EmailCampaignLog.DownloadParams): Promise<Entity.EmailCampaignLog.DownloadLink> {
    const { data } = await emailCampaignLogApi.download(params)
    return data
  }

  return {
    ui: {
      loading,
      startLoading,
      endLoading,
    },

    state: {
      eventId,
      dataList,
    },

    actions: {
      getList,
      download,
    },
  }
})
