import { ref } from 'vue'
import { defineStore } from 'pinia'
import { useEmailCampaignApi } from '@/service/api/email-campaign'
import { defaultCriteriaValue } from '@/utils/default-values'
import { useBoolean, useMatchType } from '@/hooks'

export const useEmailCampaignStore = defineStore('emailCampaignStore', () => {
  const { matchType } = useMatchType()
  const emailCampaignApi = useEmailCampaignApi()

  const formType = ref<ModalType>()
  const { bool: drawerVisible, setTrue: openDrawer, setFalse: closeDrawer } = useBoolean()
  const { bool: formModalVisible, setTrue: openFormModal, setFalse: closeFormModal } = useBoolean()
  const { bool: ticketModalVisible, setTrue: openTicketModal, setFalse: closeTicketModal } = useBoolean()

  const dataList = ref<Entity.EmailCampaign[]>([])
  const searchKeyword = ref<string | null>(null)
  const eventId = ref<number>(0)

  const selectedData = ref<Entity.EmailCampaign>()

  const defaultSearchModel: Criteria<string>[] = defaultCriteriaValue()

  const defaultFormModel: Entity.EmailCampaign.CreateUpdateParams = {
    name: '',
    description: '',
    emailLayout: '',
    eventId: 0,
    supportEmail: '',
    emailTemplateTypeId: 1,
  }

  const formModel = ref<Nullable<Entity.EmailCampaign.CreateUpdateParams>>(defaultFormModel)
  const searchModel = ref<Criteria<string>[]>(defaultSearchModel)

  async function getList(reset: boolean = false): Promise<boolean> {
    if (reset) {
      searchModel.value = defaultCriteriaValue()
    }

    const { success, data } = await emailCampaignApi.list(eventId.value, { criteria: searchModel.value, keyword: searchKeyword.value, matchType: matchType.value, paginate: false })
    dataList.value = data.list
    return success
  }

  async function get(reset: boolean = false): Promise<boolean> {
    if (reset) {
      searchModel.value = defaultCriteriaValue()
    }

    const { success, data } = await emailCampaignApi.get(eventId.value)
    dataList.value = data.list
    return success
  }

  async function create(params: Entity.EmailCampaign.CreateUpdateParams): Promise<boolean> {
    params.eventId = eventId.value
    const { success } = await emailCampaignApi.create(params)
    if (success) {
      resetFormModel()
    }
    return success
  }

  async function update(id: number, params: Entity.EmailCampaign.CreateUpdateParams): Promise<boolean> {
    const { success } = await emailCampaignApi.update(id, params)
    return success
  }

  async function del(id: number): Promise<boolean> {
    const { success } = await emailCampaignApi.del(id)
    return success
  }

  async function sendEmail(data: Entity.EmailCampaign.SendEmailParams): Promise<boolean> {
    const { success } = await emailCampaignApi.sendEmail(data)
    return success
  }

  function handleResetSearch() {
    searchModel.value = { ...defaultSearchModel }
  }

  function resetFormModel() {
    formModel.value = {
      name: null,
      description: null,
    	emailLayout: '',
      eventId: 0,
      supportEmail: null,
      emailTemplateTypeId: 1,
    }
  }

  function reset() {
    dataList.value = []
    eventId.value = 0
  }

  return {
    ui: {
      formModalVisible,
      openFormModal,
      closeFormModal,
      drawerVisible,
      openDrawer,
      closeDrawer,
      ticketModalVisible,
      openTicketModal,
      closeTicketModal,
    },

    state: {
      eventId,
      selectedData,
      dataList,
      formModel,
      searchModel,
      searchKeyword,
      matchType,
      defaultFormModel,
      formType,
    },

    actions: {
      reset,
      resetFormModel,
      handleResetSearch,
      get,
      getList,
      create,
      update,
      del,
      sendEmail,
    },
  }
})
