import { getActivePinia } from 'pinia'
import { jwtDecode } from 'jwt-decode'
import { useRouteStore } from './router'
import { useTabStore } from './tab'
import { changePassword, fetchLogin } from '@/service'
import { router } from '@/router'
import { local } from '@/utils'

export const useAuthStore = defineStore('auth-store', {
  state: (): any => {
    return {
      userInfo: local.get('userInfo'),
      token: local.get('accessToken') || '',
    }
  },
  getters: {
    /** 是否登录 */
    isLogin(state) {
      return Boolean(state.token)
    },
  },

  actions: {
    /* 登录退出，重置用户信息等 */
    async logout() {
      const pinia = getActivePinia()
      if (pinia) {
        try {
          pinia._s.forEach((store) => {
            if (store.reset) {
              store.reset()
            }
            else if (store.$reset) {
              store.$reset()
            }
            else {
              console.warn(`Store ${store.$id} does not have a reset method`)
            }
          })
        }
        catch (e) {
          console.log(e)
        }
      }
      const route = unref(router.currentRoute)
      // 清除本地缓存
      this.clearAuthStorage()
      // 清空路由、菜单等数据
      const routeStore = useRouteStore()
      routeStore.resetRouteStore()
      // 清空标签栏数据
      const tabStore = useTabStore()
      tabStore.clearAllTabs()
      // 重置当前存储库
      this.$reset()
      // 重定向到登录页
      location.reload()
      if (route.meta.requiresAuth) {
        router.push({
          name: 'login',
          query: {
            redirect: route.fullPath,
          },
        })
      }
    },
    clearAuthStorage() {
      local.remove('idToken')
      local.remove('accessToken')
      local.remove('refreshToken')
      local.remove('userInfo')
      localStorage.removeItem('companyId')
    },

    /* 用户登录 */
    async login(email: string, password: string) {
      try {
        const { success, data } = await fetchLogin({ email, password })
        if (!success)
          return

        // 处理登录信息
        await this.handleLoginInfo(data)
      }
      catch (e) {
        console.warn('[Login Error]:', e)
      }
    },

    /* 处理登录返回的数据 */
    async handleLoginInfo(data: string) {
      // 将token和userInfo保存下来
      const userInfo: any = jwtDecode(data)

      local.set('userInfo', userInfo)
      local.set('idToken', data)
      local.set('accessToken', data)
      this.token = data
      this.userInfo = userInfo

      // 添加路由和菜单
      const routeStore = useRouteStore()
      await routeStore.initAuthRoute()

      // 进行重定向跳转
      const route = unref(router.currentRoute)
      const query = route.query as { redirect: string }
      router.push({
        path: query.redirect || '/',
      })
    },

    async forgotPassword(): Promise<ApiResponse<boolean>> {
      const res = await changePassword({ email: this.userInfo.email })
      return res
    },
  },
})
