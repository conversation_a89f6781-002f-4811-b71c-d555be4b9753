<script setup lang="tsx">
import { Icon } from '@iconify/vue'
import type { DataTableColumns } from 'naive-ui'
import { useLicenseStore } from '@/store/license'
import { useEventStore } from '@/store/event'
import { formatDate } from '@/utils'
import YellowLime from '@/assets/yellow-lime.svg'

const eventStore = useEventStore()
const licenseStore = useLicenseStore()

const loading = ref<boolean>(false)

const columns: DataTableColumns<Entity.LicenseBlock> = [
  { title: 'Total License', align: 'left', key: 'total' },
  { title: 'License Type', align: 'left', key: 'licenseType.name' },
  { title: 'In-Use', align: 'left', key: 'inUse' },
  { title: 'Available', align: 'left', key: 'available' },
  { title: 'Expiry Date', align: 'left', key: 'expiryDate', render(row: Entity.LicenseBlock) {
    return formatDate(row.expiryDate)
  } },
]

onMounted(async () => {
  if (eventStore.selectedEvent?.id) {
    loading.value = true
    await licenseStore.fetchLicense(eventStore.selectedEvent?.id || 0)
    loading.value = false
  }
})
</script>

<template>
  <div v-if="!loading">
    <n-collapse arrow-placement="right">
      <template #arrow>
        <Icon icon="mdi:chevron-right" class="text-[#A2A7AF] text-[3rem]" />
      </template>

      <div v-for="item in licenseStore.licenseList" :key="item.id" class="relative">
        <div class="absolute left-[-10px] top-[-10px] w-30px">
          <n-image :src="YellowLime" preview-disabled class="w-100% h-100% rotate-[335deg]" />
        </div>
        <n-collapse-item :title="item.name" :name="item.id" arrow-placement="right" class="bg-white py-4 px-8 sm:px-12 rounded-theme shadow">
          <template #header>
            <span class="text-2xl font-500">{{ item.name }}</span>
          </template>

          <div style="white-space: pre;" class="mb-4">
            <n-data-table
              :columns="columns"
              :data="item.licenseBlocks"
              :loading="loading"
              :bordered="false"
              class="shadow rounded-theme"
            />
          </div>
        </n-collapse-item>
      </div>
    </n-collapse>
  </div>
  <div v-else>
    <n-collapse arrow-placement="right">
      <template #arrow>
        <Icon icon="mdi:chevron-right" class="text-[#A2A7AF] text-[3rem]" />
      </template>
      <div v-for="item in [1]" :key="item" class="relative">
        <div class="absolute left-[-10px] top-[-10px] w-30px">
          <n-image :src="YellowLime" preview-disabled class="w-100% h-100% rotate-[335deg]" />
        </div>
        <n-collapse-item arrow-placement="right" class="bg-white py-4 px-8 sm:px-12 rounded-theme shadow">
          <template #header>
            <n-skeleton text style="width: 100%" />
          </template>
        </n-collapse-item>
      </div>
    </n-collapse>
  </div>
</template>

<style>
.n-collapse .n-collapse-item .n-collapse-item__header .n-collapse-item__header-main {
  justify-content: space-between !important;
}

.n-collapse .n-collapse-item .n-collapse-item__header{
  padding:0 !important;
}

.n-collapse .n-collapse-item:not(:first-child){
  border: unset !important;
}
</style>
