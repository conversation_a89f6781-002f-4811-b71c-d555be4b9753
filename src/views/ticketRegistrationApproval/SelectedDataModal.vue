<script setup lang="ts">
import { defineProps } from 'vue'
import type { DataTableColumns } from 'naive-ui'

interface Props {
  title?: string
  showModal: boolean
  columns: DataTableColumns<any>
  selectedRowsData: any
  action?: void
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Selected Data',
})

const emit = defineEmits<{
  (e: 'update:showModal', value: boolean): void
}>()

const modalVisible = computed({
  get: () => props.showModal,
  set: (visible: boolean) => emit('update:showModal', visible),
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :title="title"
    :mask-closable="true"
    class="modal-container"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="rounded-theme">
      <template #header>
        <div class="flex flex-col">
          <div class="flex justify-between">
            <n-flex horizontal align="center">
              <icon-park-outline-box />
              <b class="uppercase">{{ title }}</b>
            </n-flex>
            <small class="text-[#707070]">{{ selectedRowsData.length }} row(s) selected</small>
          </div>
        </div>
      </template>

      <div class="table-wrapper" style="white-space: pre;">
        <n-data-table
          :data="selectedRowsData"
          :columns="columns"
          :loading="false"
          class="rounded-theme"
          ellipsis
        />
      </div>

      <template #action>
        <div class="flex justify-between">
          <n-button type="default" class="shadow" @click="modalVisible = false">
            Cancel
          </n-button>

          <template v-if="action">
            <n-button type="default" class="shadow" @click="action">
              Next
            </n-button>
          </template>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped>
.modal-container {
  min-width: 300px;
  max-width: 90%;
  max-height: 90vh;
  min-height: 300px;
  overflow: hidden;
}

.table-wrapper {
  white-space: 'pre';
  max-height: 60vh; /* Adjust the max height to your preference */
  overflow-y: auto; /* Enables scrolling if data exceeds the max-height */
  margin-top: 16px;
}

.n-data-table {
  min-width: 100%;
  table-layout: fixed; /* Ensures columns maintain a consistent layout */
}

@media (max-width: 768px) {

  .table-wrapper {
    max-height: 50vh;
  }
}
</style>
