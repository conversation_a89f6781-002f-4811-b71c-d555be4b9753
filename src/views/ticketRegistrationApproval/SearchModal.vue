<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue'
import { $t } from '@/utils/i18n'
import { useCriteriaStore } from '@/store/criteria'
import { useTicketTypeStore } from '@/store/ticket-type'
import { useEventStore } from '@/store/event'
import { useTicketRegistrationApprovalStore } from '@/store/ticket-registration-approval'

// Interface declaration
interface Props {
  visible: boolean
  modalData?: Entity.Ticket | null
  onSearch: () => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// End of Type and Interface declaration

const eventStore = useEventStore()
const ticketStore = useTicketRegistrationApprovalStore()
const ticketTypeStore = useTicketTypeStore()
const criteriaStore = useCriteriaStore(ticketStore.searchModel)

const approvalStatusOptions = computed(() => {
  return [
    { label: 'Pending', value: 1 },
    { label: 'Approved', value: 2 },
    { label: 'Rejected', value: 3 },
  ]
})

const columnOptions = computed(() => [
  { label: 'Name', value: 'holderName' },
  { label: 'Email', value: 'email' },
  { label: 'Company Name', value: 'companyName' },
  { label: 'Contact Number', value: 'contactNumber' },
  { label: 'Custom Note', value: 'customNote' },
  { label: 'Approval Status', value: 'approvalStatusId' },
])

function getOptions(columnValue) {
  if (columnValue === 'approvalStatusId') {
    return approvalStatusOptions.value
  }

  return criteriaStore.criteriaOptions.filter(option =>
    ['equal', 'contain', 'notEqual'].includes(option.value),
  )
}

function getValue() {
  return criteriaStore.criteriaOptions.filter(option =>
    ['equal', 'contain', 'notEqual'].includes(option.value),
  )
}

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

onBeforeMount(() => {
  ticketTypeStore.eventId = eventStore.selectedEvent?.id || 0
})

watch(
  () => ticketStore.searchModel,
  (newSearchModel) => {
    newSearchModel.forEach((criterion) => {
      watch(
        () => criterion.column,
        (newColumn) => {
          if (newColumn) {
            criterion.type = null
            criterion.value = null
          }
        },
      )
    })
  },
  { deep: true },
)
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    :segmented="{ content: true, action: true }"
  >
    <n-card
      class="w-700px min-h-400px"
    >
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-filter />
              <b class="uppercase mt-1">Advanced Filter</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            Criteria filter lets you refine searches with custom conditions, helping you pinpoint specific data or products based on multiple attributes
          </div>
        </n-flex>
      </template>

      <n-radio-group v-model:value="ticketStore.matchType" name="matchType" class="mt-2">
        <n-radio value="all">
          {{ $t('common.allOfTheFollowing') }}
        </n-radio>
        <n-radio value="any">
          {{ $t('common.anyOfTheFollowing') }}
        </n-radio>
      </n-radio-group>

      <div>
        <div v-for="(criterion, index) in ticketStore.searchModel" :key="index">
          <div class="flex flex-col sm:flex-row gap-x-2 gap-y-2 sm:gap-y-0 mt-4">
            <n-select v-model:value="criterion.column" placeholder="Select Column" :options="columnOptions" />

            <!-- Conditionally render type select based on column options -->
            <template v-if="!['asd'].includes(String(criterion.column))">
              <n-select v-model:value="criterion.type" placeholder="Select Type" :options="getValue()" />
            </template>

            <!-- Conditionally render input or select based on column options -->
            <template v-if="['ticketTypeIds', 'approvalStatusId'].includes(String(criterion.column))">
              <n-select v-model:value="criterion.value" :options="getOptions(criterion.column)" />
            </template>
            <template v-else>
              <n-input v-model:value="criterion.value" placeholder="Value" />
            </template>

            <n-button type="error" ghost @click="criteriaStore.removeCriteria(index)">
              <icon-park-outline-delete />
            </n-button>
          </div>
        </div>
        <div class="flex gap-2">
          <n-button class="mt-4 bg-theme" type="primary" :bordered="false" @click="criteriaStore.addCriteria">
            Add Criteria
          </n-button>
          <n-button class="mt-4 me-4" type="error" :bordered="false" @click="criteriaStore.clearAll">
            Clear All
          </n-button>
        </div>
      </div>

      <template #action>
        <n-flex justify="left">
          <n-button @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" class="bg-theme" :bordered="false" @click="onSearch">
            Apply
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
