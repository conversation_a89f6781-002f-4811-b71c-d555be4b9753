<script setup lang="tsx">
import type { DataTableColumns, DataTableInst, DataTableSortState } from 'naive-ui'
import { NButton, NSpace } from 'naive-ui'
import { ref } from 'vue'
import { Icon } from '@iconify/vue'
import SelectedDataModal from './SelectedDataModal.vue'
import SearchModal from './SearchModal.vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { useTicketRegistrationApprovalStore } from '@/store/ticket-registration-approval'
import { useEventStore } from '@/store/event'
import ConfirmModal from '@/views/ticket/ConfirmModal.vue'
import { formatDate, renderIcon } from '@/utils'
import { getSortKey } from '@/utils/default-values'
import { useTicketTypeStore } from '@/store/ticket-type'

// types
type Operation = 'approve' | 'reject' | 'delete'

// loading states
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: exportLoading, setTrue: startExportLoading, setFalse: endExportLoading } = useBoolean(false)

// visibility states
const { bool: selectTicketTypeModalVisible, setTrue: openSelectTicketTypeModal, setFalse: closeSelectTicketTypeModal } = useBoolean(false)
const { bool: selectedDataModalVisible, setTrue: openSelectedDataModal, setFalse: closeSelectedDataModal } = useBoolean(false)
const { bool: ticketDrawerVisible, setTrue: openTicketDrawer, setFalse: closeTicketDrawer } = useBoolean(false)
const { bool: confirmModalVisible, setTrue: openConfirmModal, setFalse: closeConfirmModal } = useBoolean(false)
const { bool: searchModalVisible, setTrue: openSearchModal, setFalse: closeSearchModal } = useBoolean(false)

// stores
const eventStore = useEventStore()
const ticketStore = useTicketRegistrationApprovalStore()
const ticketTypeStore = useTicketTypeStore()

// etc
const tableRef = ref<DataTableInst>()
const eventId = ref<number>(0)
const confirmModalInfo = ref<ConfirmModalInfo>()
const checkedRowKeysRef = ref<Array<string | number>>([])
const selectedRowsData = ref<Entity.TicketRegistrationApproval[]>([])
const action = ref<(() => void)>(() => window.$message.warning('No action selected'))

const columns: DataTableColumns<Entity.TicketRegistrationApproval> = [
  {
    type: 'selection',
    options: [
      'all',
      'none',
    ],
  },
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row: Entity.TicketRegistrationApproval, index: number) {
      return index + 1 + ((ticketStore.currentPage - 1) * ticketStore.currentSize)
    },
  },
  { title: $t('ticket.name'), align: 'center', key: 'holderName', sorter: true },
  { title: $t('ticket.email'), align: 'center', key: 'email', className: 'max-w-200px overflow-ellipsis', sorter: true },
  { title: $t('ticket.companyName'), align: 'center', key: 'companyName', className: 'max-w-200px overflow-ellipsis', sorter: true },
  { title: $t('ticket.age'), align: 'center', key: 'age' },
  { title: $t('ticket.gender'), align: 'center', key: 'gender' },
  { title: $t('ticket.contactNumber'), align: 'center', key: 'contactNumber' },
  {
    title: $t('ticket.status'),
    align: 'center',
    key: 'approvalStatusId',
    className: 'w-180px overflow-ellipsis',
    sorter: true,
    render(row: Entity.TicketRegistrationApproval) {
      const badgeClasses = {
        APPROVED: 'badge-approved',
        REJECTED: 'badge-rejected',
        PENDING: 'badge-pending',
      }
      return (
        <button class={`${badgeClasses[row.approvalStatus?.name || 'pending']} mx-auto px-4 py-1 fw-semibold rounded-10px  flex items-center text-center focus:outline-none text-[14px]`}>
          <Icon icon="mdi:circle" class="mr-2" />
          {row.approvalStatus?.name || 'Unknown'}
        </button>
      )
    },
  },
  { title: $t('ticket.createdAt'), align: 'center', key: 'createdAt', render(row: Entity.TicketRegistrationApproval) {
    return formatDate(row.createdAt)
  } },
]

const dropdownOptions = [
  { key: 'delete', label: 'Delete', icon: renderIcon('icon-park-outline:delete') },
  { key: 'export', label: 'Export', icon: renderIcon('uil:export') },
  { key: 'clearSort', label: 'Clear Sort', icon: renderIcon('mdi:sort-variant-remove') },
]

async function fetchTicketList(reset: boolean = false) {
  ticketStore.ticketRegistrationApprovalList = []
  startLoading()
  window.$loadingBar.start()
  await ticketStore.fetchTicketRegistrationApprovalList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handleBulkOperation(operation: Operation) {
  if (operation === 'approve' && !ticketStore.ticketTypeId) {
    window.$message.warning('Please select a ticket type before proceeding.')
    return
  }

  const hasOtherThanPending = ticketStore.ticketRegistrationApprovalList
    .filter(item => checkedRowKeysRef.value.includes(item.id))
    .some(item => item.approvalStatusId !== 1)
  if (['approve', 'reject'].includes(operation) && hasOtherThanPending) {
    window.$message.warning('Action cannot be completed due to selection of tickets with invalid ticket status. Please verify the selected tickets and try again.')
    return
  }

  if (checkedRowKeysRef.value.length === 0) {
    window.$message.warning('Please select at least one ticket registration to proceed.')
    return false
  }

  openSelectedDataModal()

  // Ensure approval status ids are the same to operate action
  const approvalIds = ticketStore.ticketRegistrationApprovalList
    .filter(item => checkedRowKeysRef.value.includes(item.id))
    .map(item => item.approvalStatusId)
  const isAllSame = approvalIds.every(statusId => statusId === approvalIds[0])

  if (!isAllSame && operation !== 'delete') {
    window.$message.warning('Action cannot be completed due to selection of different approval statuses. Please verify the selected tickets and try again.')
    return false
  }

  assignAction(operation)
}

async function handleOperation(operation?: Operation): Promise<boolean> {
  openConfirmModal()

  if (operation) {
    assignAction(operation)
  }

  return false
}

function assignAction(operation: Operation) {
  const operationMap: Record<Operation, () => void> = {
    approve: () => approveTicketRegistration(checkedRowKeysRef.value),
    reject: () => rejectTicketRegistration(checkedRowKeysRef.value),
    delete: deleteTicketRegistration,
  }

  action.value = operationMap[operation]

  const operationText = operation.charAt(0).toUpperCase() + operation.slice(1)
  const message = checkedRowKeysRef.value.length === 0
    ? `Are you sure you want to ${operationText} all ticket registrations? This action cannot be undone once executed.`
    : `Are you sure you want to ${operationText} the selected tickets? This action cannot be undone once executed.`

  // Set modal information
  confirmModalInfo.value = {
    ...confirmModalInfo.value,
    title: `Confirm ${operationText} Ticket Registration`,
    message,
    confirmText: 'Confirm',
    cancelText: 'Cancel',
  }
}

async function approveTicketRegistration(ids: Array<string | number>) {
  await ticketStore.approveTicketRegistration(ids)
  ticketStore.handleResetSearch()
  window.$message.success('Approved successfully!')
  checkedRowKeysRef.value = []
  fetchTicketList()
}

async function rejectTicketRegistration(ids: Array<string | number>) {
  await ticketStore.rejectTicketRegistration(ids)
  ticketStore.handleResetSearch()
  window.$message.success('Rejected successfully!')
  checkedRowKeysRef.value = []
  fetchTicketList()
}

async function deleteTicketRegistration() {
  await ticketStore.deleteTicketRegistration(checkedRowKeysRef.value)
  ticketStore.handleResetSearch()
  window.$message.success('Deleted successfully!')
  checkedRowKeysRef.value = []
  fetchTicketList()
}

async function exportTicket() {
  startExportLoading()
  const url = await ticketStore.exportRegistrationApproval()
  endExportLoading()

  if (!url) {
    window.$message.error('Failed to fetch the CSV url')
    return
  }
  window.location.href = url
}

async function handlePageChange(page: number, size: number) {
  startLoading()
  await ticketStore.handlePageChange(page, size)
  endLoading()
}

function rowProps(row: Entity.TicketRegistrationApproval) {
  return {
    style: 'cursor: pointer;',
    onClick: (e: MouseEvent) => {
      const target = e.target as HTMLElement
      if (target.closest('.n-checkbox')) {
        return
      }

      openTicketDrawer()
      ticketStore.editData = row
    },
  }
}

async function handleSorterUpdate(options: DataTableSortState | DataTableSortState[] | null) {
  if (!options)
    return

  const isArray = Array.isArray(options)
  if (!isArray && !options.order) {
    await fetchTicketList(true)
    return
  }

  ticketStore.sort = isArray
    ? options.map(option => ({ column: option.columnKey.toString(), order: getSortKey(option.order) }))
    : [{ column: options.columnKey.toString(), order: getSortKey(options.order) }]

  await fetchTicketList(false)
}

async function handleClearSort() {
  tableRef.value?.clearSorter()
  await fetchTicketList(true)
}

function handleApprove() {
  const hasOtherThanPending = ticketStore.ticketRegistrationApprovalList
    .filter(item => checkedRowKeysRef.value.includes(item.id))
    .some(item => item.approvalStatusId !== 1)

  if (hasOtherThanPending) {
    window.$message.warning('Action cannot be completed due to selection of tickets with invalid ticket status. Please verify the selected tickets and try again.')
    return
  }

  openSelectTicketTypeModal()
}

async function handleCriteriaFilter() {
  fetchTicketList(false)
  closeSearchModal()
}

function handleDropdownSelect(key: string | number) {
  if (key === 'delete') {
    handleBulkOperation('delete')
  }
  else if (key === 'export') {
    exportTicket()
  }
  else if (key === 'clearSort') {
    handleClearSort()
  }
}

const ticketTypeOptions = computed(() =>
  (ticketTypeStore.ticketTypeList.map(ticketType => ({
    label: `${ticketType.name}`,
    value: ticketType.id,
  }))) || [],
)

onBeforeMount(() => {
  ticketStore.searchKeyword = null
  eventId.value = eventStore.selectedEvent?.id ?? 0
  ticketTypeStore.eventId = eventStore.selectedEvent?.id ?? 0
})

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0)
    return

  ticketStore.eventId = Number(newValue)

  fetchTicketList()
  await ticketTypeStore.fetchTicketTypeList()
})

watch(checkedRowKeysRef, (newCheckedKeys) => {
  newCheckedKeys.forEach((key) => {
    const row = ticketStore.ticketRegistrationApprovalList.find(item => item.id === key)
    if (row && !selectedRowsData.value.some(item => item.id === row.id)) {
      selectedRowsData.value.push(row)
    }
  })

  selectedRowsData.value = selectedRowsData.value.filter(row => newCheckedKeys.includes(row.id))
})
</script>

<template>
  <NSpace size="large" vertical class="flex-1">
    <n-card class="shadow rounded-theme">
      <NSpace vertical size="large">
        <div class="flex flex-wrap gap-4">
          <n-input-group class="sm:min-w-80 w-auto">
            <n-input v-model:value="ticketStore.searchKeyword" :placeholder="`${$t('common.search')} ${$t('common.keyword')}`" clearable @keyup.enter="fetchTicketList(true)">
              <template #prefix>
                <div>
                  <icon-park-outline-search />
                </div>
              </template>
            </n-input>
          </n-input-group>

          <NButton ghost @click="handleApprove()">
            <template #icon>
              <Icon icon="mdi:tick" />
            </template>
            Approve
          </NButton>

          <NButton ghost @click="handleBulkOperation('reject')">
            <template #icon>
              <Icon icon="mdi:times" />
            </template>
            Reject
          </NButton>

          <NDropdown trigger="click" :options="dropdownOptions" placement="bottom-start" @select="handleDropdownSelect">
            <NButton ghost :loading="exportLoading">
              <template #icon>
                <icon-park-outline-more-one />
              </template>
              Actions
            </NButton>
          </NDropdown>

          <NButton ghost class="ml-auto" @click="openSearchModal">
            <template #icon>
              <icon-park-outline-filter />
            </template>
            Filter
          </NButton>

          <NButton ghost @click="openSelectedDataModal">
            <template #icon>
              <Icon icon="icon-park-outline:list" />
            </template>
            Show Selected Data
          </NButton>
        </div>

        <div style="white-space: pre;" class="mt-2">
          <n-data-table
            ref="tableRef"
            v-model:checked-row-keys="checkedRowKeysRef"
            :loading="loading"
            :columns="columns"
            :data="ticketStore.ticketRegistrationApprovalList"
            class="shadow rounded-theme" :row-props="rowProps"
            :row-key="(row) => row.id"
            @update:sorter="handleSorterUpdate"
          />
        </div>

        <div class="flex justify-between">
          <Pagination
            :count="ticketStore.totalItems"
            :page="ticketStore.currentPage"
            :page-size="ticketStore.currentSize"
            @change="handlePageChange"
          />
          <div class="text-gray-400">
            {{ ticketStore.totalItems }} Ticket(s)
          </div>
        </div>
      </NSpace>
    </n-card>

    <n-drawer v-model:show="ticketDrawerVisible" :width="500" class="custom-drawer">
      <n-drawer-content>
        <div class="flex flex-col h-full">
          <div class="pt-4 px-4">
            <div class="flex flex-wrap my-4 justify-between items-center">
              <span class="text-2xl fw-bold">
                {{ ticketStore.editData?.holderName }}
              </span>
              <div class="flex gap-2">
                <template v-if="ticketStore.editData?.approvalStatusId === 1">
                  <NButton
                    ghost @click="() => {
                      checkedRowKeysRef = [ticketStore.editData?.id ?? '']
                      handleApprove();
                    }"
                  >
                    <template #icon>
                      <Icon icon="mdi:tick" />
                    </template>
                    Approve
                  </NButton>

                  <NButton
                    ghost @click="async () => {
                      console.log('ahha')
                      checkedRowKeysRef = [ticketStore.editData?.id ?? '']
                      const flag = await handleOperation('reject')
                      if (flag){
                        checkedRowKeysRef = []
                      }
                    }"
                  >
                    <template #icon>
                      <Icon icon="mdi:times" />
                    </template>
                    Reject
                  </NButton>
                </template>

                <template v-else>
                  <NButton
                    ghost @click="async () => {
                      checkedRowKeysRef = [ticketStore.editData?.id ?? '']
                      const flag = await handleOperation('delete')
                      if (flag){
                        checkedRowKeysRef = []
                      }
                    }"
                  >
                    <template #icon>
                      <Icon icon="icon-park-outline:delete" />
                    </template>
                    Delete
                  </NButton>
                </template>
              </div>
            </div>
          </div>

          <n-divider class="px-4" />

          <n-flex vertical class="p-4">
            <n-flex align="center" class="space-x-4 mb-4">
              <div class="min-w-40">
                Email:
              </div>
              <div class="font-bold">
                {{ ticketStore.editData?.email }}
              </div>
            </n-flex>

            <n-flex align="center" class="space-x-4 mb-4">
              <div class="min-w-40">
                Age:
              </div>
              <div class="font-bold">
                {{ ticketStore.editData?.age }}
              </div>
            </n-flex>

            <n-flex align="center" class="space-x-4 mb-4">
              <div class="min-w-40">
                Company Name:
              </div>
              <div class="font-bold">
                {{ ticketStore.editData?.companyName }}
              </div>
            </n-flex>

            <n-flex align="center" class="space-x-4 mb-4">
              <div class="min-w-40">
                Contact Number:
              </div>
              <div class="font-bold">
                {{ ticketStore.editData?.contactNumber }}
              </div>
            </n-flex>

            <n-flex align="center" class="space-x-4 mb-4">
              <div class="min-w-40">
                Gender:
              </div>
              <div class="font-bold">
                {{ ticketStore.editData?.gender }}
              </div>
            </n-flex>
          </n-flex>

          <div class="flex-1 bg-[#f2f2f2] mt-4 p-4 pt-8" style="border-top-left-radius: 20px; border-top-right-radius: 20px">
            <div class="text-2xl fw-bold">
              Custom Note
            </div>

            <div class="mt-4">
              <template v-if="(ticketStore.editData?.customNote?.length || 0) > 0">
                <div v-for="(note, index) in ticketStore.editData?.customNote" :key="index">
                  <div class="flex space-x-4 mb-4">
                    <div class="min-w-40">
                      {{ note.key }}
                    </div>
                    <div class="font-bold">
                      {{ note.value }}
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <span><strong>N/A</strong></span>
              </template>
            </div>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>

    <SelectedDataModal
      :columns="columns.filter((_, index) => ![0, 1].includes(index))"
      :show-modal="selectedDataModalVisible"
      :selected-rows-data="selectedRowsData"
      :action="openConfirmModal"
      @update:show-modal="val => selectedDataModalVisible = val"
    />

    <ConfirmModal
      :show-modal="confirmModalVisible"
      :title="confirmModalInfo?.title"
      :message="confirmModalInfo?.message"
      :confirm-button-text="confirmModalInfo?.confirmText"
      :cancel-button-text="confirmModalInfo?.cancelText"
      @confirm="() => { action(); closeSelectedDataModal(); closeTicketDrawer(); closeSelectTicketTypeModal() }"
      @cancel="closeConfirmModal"
      @update:show-modal="val => confirmModalVisible = val"
    />

    <SearchModal
      v-model:visible="searchModalVisible"
      :on-search="handleCriteriaFilter"
    />

    <n-modal v-model:show="selectTicketTypeModalVisible" style="width: 500px">
      <n-card
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="rounded-theme"
      >
        <template #header>
          <n-flex vertical>
            <n-flex horizontal align="center" justify="space-between">
              <n-flex horizontal align="center">
                <b>Select Ticket Type</b>
                <icon-park-outline-check-correct class="mb-1 text-xl" />
              </n-flex>
              <icon-park-outline-close-small class="cursor-pointer" @click="closeSelectTicketTypeModal" />
            </n-flex>
          </n-flex>
        </template>
        <div class="text-sm text-[#707070]">
          Assign ticket type for the selected ticket registration(s). This action cannot be undone once executed.
          <n-select v-model:value="ticketStore.ticketTypeId" :options="ticketTypeOptions" placeholder="Select Ticket Type" class="mt-4" />
        </div>
        <template #footer>
          <NSpace justify="end">
            <NButton @click="closeSelectTicketTypeModal">
              Cancel
            </NButton>
            <NButton
              type="primary" class="bg-theme" :bordered="false" @click="handleBulkOperation('approve')"
            >
              Continue
            </NButton>
          </NSpace>
        </template>
      </n-card>
    </n-modal>
  </NSpace>
</template>

<style>
.custom-drawer .n-drawer-body-content-wrapper{
  padding:0 !important;
}
</style>
