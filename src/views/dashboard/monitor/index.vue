<script setup lang="ts">
import Chart from './components/chart.vue'
import TicketTypeChart from './components/ticketTypeChart.vue'
import TableFilledChart from './components/tableFilledChart.vue'
import GenderAgeChart from './components/GenderAgeChart.vue'
import EventTimeline from './components/EventTimeline.vue'
import Lime from '@/assets/lime.svg'
import { useDashboardStore } from '@/store/dashboard'
import StackLime from '@/assets/stack-lime.png'
import { useEventStore } from '@/store/event'
import { useBoolean } from '@/hooks'
import { useEventDetailStore } from '@/store/event-detail'

// stores
const eventStore = useEventStore()
const dashboardStore = useDashboardStore()
const eventDetailStore = useEventDetailStore()

// loading states
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// etc
const eventId = ref<number>(0)
const dashboardSummary = ref<any>()
const checkInSummary = ref<Entity.CheckIn.Summary>()

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0) {
    endLoading()
    return
  }

  eventDetailStore.eventId = eventId.value
  await eventDetailStore.fetchEventDetailList()

  dashboardSummary.value = await dashboardStore.fetchDashboardSummary(eventId.value)
  endLoading()
})

onBeforeMount(() => {
  startLoading()
  eventId.value = eventStore.selectedEvent?.id || 0
  dashboardSummary.value = undefined
  checkInSummary.value = undefined
})
</script>

<template>
  <div v-if="!loading">
    <div class="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-4 gap-6">
      <n-card :bordered="false" class="rounded-theme shadow">
        <n-space align="center">
          <n-icon color="#7f9a66" size="42" class="me-6">
            <icon-park-outline-ticket />
          </n-icon>
          <n-statistic label="Tickets">
            <n-number-animation
              :from="0"
              :to="dashboardSummary.ticket.totalTicket"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>

      <n-card :bordered="false" class="rounded-theme shadow">
        <n-space align="center">
          <n-icon color="#7f9a66" size="42" class="me-6">
            <icon-park-outline-chart-graph />
          </n-icon>
          <n-statistic label="Lucky Draws">
            <n-number-animation
              :from="0"
              :to="dashboardSummary.totalLuckyDraw"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>

      <n-card :bordered="false" class="rounded-theme shadow">
        <n-space
          align="center"
        >
          <n-icon
            color="#7f9a66"
            size="42"
            class="me-6"
          >
            <icon-park-outline-average />
          </n-icon>
          <n-statistic label="Tables">
            <n-number-animation
              :from="0"
              :to="dashboardSummary.table?.totalTable || 0"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>

      <n-card :bordered="false" class="rounded-theme shadow">
        <n-space
          align="center"
        >
          <n-icon
            color="#7f9a66"
            size="42"
            class="me-6"
          >
            <icon-park-outline-chart-pie />
          </n-icon>
          <n-statistic label="Vendors">
            <n-number-animation
              :from="0"
              :to="dashboardSummary.totalVendor"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>
    </div>

    <div class="grid gap-[30px] mt-[30px] grid-cols-1 2xl:grid-cols-12">
      <div class="col-span-12 2xl:col-span-9">
        <n-card :bordered="false" class="rounded-theme relative shadow">
          <img :src="StackLime" class="absolute top-[-10px] left-[-10px] w-[40px] object-cover">
          <Chart :event-detail="dashboardSummary?.eventDetails" />
        </n-card>
      </div>

      <div class="col-span-12 2xl:col-span-3">
        <n-card :bordered="false" class="rounded-theme relative shadow">
          <TableFilledChart
            :data="dashboardSummary?.table || {
              totalTable: 0,
              totalSeat: 0,
              free: {
                totalTable: 0,
                totalSeat: 0,
                percentage: 0,
              },
              occupied: {
                totalTable: 0,
                totalSeat: 0,
                percentage: 0,
              },
              full: {
                totalTable: 0,
                totalSeat: 0,
                percentage: 0,
              },
            }"
          />
        </n-card>
      </div>

      <div class="col-span-12 2xl:col-span-6">
        <n-card :bordered="false" class="rounded-theme relative shadow">
          <GenderAgeChart :total-gender-by-age-range="dashboardSummary.ticket.totalGenderByAgeRange" />
        </n-card>
      </div>

      <div class="col-span-12 2xl:col-span-3">
        <n-card :bordered="false" class="rounded-theme relative shadow">
          <TicketTypeChart :data="dashboardSummary?.ticket?.types" />
        </n-card>
      </div>

      <div class="col-span-12 2xl:col-span-3">
        <n-card :bordered="false" class="rounded-theme relative shadow">
          <EventTimeline />
        </n-card>
      </div>
    </div>
  </div>
  <div v-else class="flex justify-center items-center">
    <div id="loading-container">
      <n-image :src="Lime" alt="" width="50" height="50" />
      <div class="loader" />
    </div>
  </div>
</template>

<style scoped>
  #loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 40px;
  }

  .loader {
    display: block;
    --height-of-loader: 8px;
    --loader-color: #7f9a66;
    width: 200px;
    height: var(--height-of-loader);
    border-radius: 30px;
    background-color: rgba(0,0,0,0.2);
    position: relative;
  }

  .loader::before {
    content: "";
    position: absolute;
    background: var(--loader-color);
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    border-radius: 30px;
    animation: moving 1s ease-in-out infinite;
    ;
  }

  @keyframes moving {
    50% {
      width: 100%;
    }

    100% {
      width: 0;
      right: 0;
      left: unset;
    }
  }
</style>
