<script setup lang="ts">
import { defineProps, ref, watch } from 'vue'
import { type ECOption, useEcharts } from '@/hooks'

// Define the type for the data prop
interface TableData {
  totalTable: number
  totalSeat: number
  free: {
    totalTable: number
    totalSeat: number
    percentage: number
  }
  occupied: {
    totalTable: number
    totalSeat: number
    percentage: number
  }
  full: {
    totalTable: number
    totalSeat: number
    percentage: number
  }
}

// Define the incoming data prop
const props = defineProps<{ data: TableData }>()

// Initialize the chart options
const option = ref<ECOption>({
  title: {
    text: 'Tables Filled',
    left: 'start',
    top: '10px',
    textStyle: {
      fontSize: 20,
      color: '#333',
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: (params) => {
      const { name } = params
      let totalTable = 0
      let totalSeat = 0

      switch (name) {
        case 'Free':
          totalTable = props.data.free.totalTable
          totalSeat = props.data.free.totalSeat
          break
        case 'Occupied':
          totalTable = props.data.occupied.totalTable
          totalSeat = props.data.occupied.totalSeat
          break
        case 'Full':
          totalTable = props.data.full.totalTable
          totalSeat = props.data.full.totalSeat
          break
      }

      return `${name}: <br/> 
              Total Tables: ${totalTable} <br/> 
              Total Seats: ${totalSeat} <br/>`
    },
  },
  legend: {
    bottom: 'bottom',
  },
  toolbox: {
    show: true,
    feature: {
      mark: { show: true },
      dataView: { show: false, readOnly: false },
      restore: { show: false },
      saveAsImage: { show: true },
    },
  },
  series: [
    {
      name: 'Table Status',
      type: 'pie',
      radius: ['30%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: true,
        fontSize: '12px',
        color: '#333',
        position: 'outside',
        formatter: '{c}%',
      },
      labelLine: {
        show: true,
      },
      labelLayout: {
        draggable: true,
      },
      data: [
        { value: props.data.free.percentage, name: 'Free', itemStyle: { color: '#F1EFFB' } },
        { value: props.data.occupied.percentage, name: 'Occupied', itemStyle: { color: '#7F9A66' } },
        { value: props.data.full.percentage, name: 'Full', itemStyle: { color: '#B29F90' } },
      ],
    },
  ],
})

// Watch for changes in the prop and update the chart
watch(
  () => props.data,
  (newData) => {
    option.value.series[0].data = [
      { value: newData.free.percentage, name: 'Free', itemStyle: { color: '#F1EFFB' } },
      { value: newData.occupied.percentage, name: 'Occupied', itemStyle: { color: '#7F9A66' } },
      { value: newData.full.percentage, name: 'Full', itemStyle: { color: '#B29F90' } },
    ]
  },
  { immediate: true }, // To ensure the chart updates immediately when the component is mounted
)

const lineRef = ref<HTMLElement | null>(null)
useEcharts(lineRef, option)
</script>

<template>
  <div
    ref="lineRef"
    class="min-h-480px h-400px"
  />
</template>

<style scoped>

</style>
