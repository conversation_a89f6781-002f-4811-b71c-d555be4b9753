<script setup lang="ts">
import { computed, defineProps, nextTick, onMounted, ref, watch } from 'vue'
import { Line } from 'vue-chartjs'
import { CategoryScale, Chart as ChartJS, LineElement, LinearScale, PointElement, Title, Tooltip } from 'chart.js'
import moment from 'moment'

const props = defineProps<{
  eventDetail: EventDetail[]
}>()

ChartJS.register(Title, Tooltip, LineElement, CategoryScale, LinearScale, PointElement)

interface EventCheckIn {
  totalTicket: number
  totalCheckIn: number
  totalNotCheckIn: number
  totalCheckInTicketType: TicketType[]
  checkInInterval: Array<{ timeInternval: string, totalCheckIn: number }>
}

interface TicketType {
  ticketType: string
  totalTicket: number
  totalCheckIn: number
}

interface TableInfo {
  totalTable: number
  totalSeat: number
  free: {
    totalTable: number
    totalSeat: number
    percentage: number
  }
  occupied: {
    totalTable: number
    totalSeat: number
    percentage: number
  }
  full: {
    totalTable: number
    totalSeat: number
    percentage: number
  }
}

interface EventDetail {
  id: number
  name: string
  checkInStartAt: string
  checkInEndAt: string
  checkIn: EventCheckIn
  table: TableInfo
  totalLuckyDraw: number
}

const selectedEventIndex = ref(0)
const selectedEvent = computed(() => props.eventDetail[selectedEventIndex.value])

watch(() => selectedEvent.value, () => {
  nextTick(() => {
    renderChart()
  })
}, { deep: true })

const checkInData = computed(() => {
  const checkInInterval = selectedEvent.value?.checkIn?.checkInInterval || []
  return checkInInterval.map(interval => ({
    value: interval.totalCheckIn,
    time: moment(interval.timeInternval).format('YYYY-MM-DD h:mm a'),
  }))
})

// Flag to check if there is any data
const hasData = computed(() => checkInData.value.length > 0)

// Chart data and options
const chartData = computed(() => {
  const data = checkInData.value.length > 0
    ? checkInData.value
    : [{ time: 'No Data', value: 0 }] // Create a default data point for empty data

  return {
    labels: data.map(item => item.time),
    datasets: [
      {
        label: 'Check-In Count',
        data: data.map(item => item.value),
        fill: false,
        borderColor: '#7f9a66',
        tension: 0.1,
        pointRadius: 5,
        pointBackgroundColor: '#7f9a66',
      },
    ],
  }
})

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false, // Disable aspect ratio
  plugins: {
    tooltip: {
      callbacks: {
        label: (tooltipItem: any) => {
          return `Time: ${tooltipItem.label} - Check-ins: ${tooltipItem.raw}`
        },
      },
    },
    legend: {
      position: 'top',
    },
  },
  scales: {
    x: {
      title: {
        display: true,
        text: 'Time',
      },
      ticks: {
        autoSkip: true,
        maxRotation: 45,
        minRotation: 45,
        font: {
          size: 10,
          family: 'Arial',
          weight: 'normal',
        },
      },
    },
    y: {
      title: {
        display: true,
        text: 'Check-In Count',
      },
      grid: {
        drawOnChartArea: true,
      },
      ticks: {
        stepSize: 1,
        callback(value: number) {
          return value % 1 === 0 ? value : ''
        },
      },
      min: 0,
    },
  },
}))

const eventOptions = computed(() =>
  props.eventDetail.map((event, index) => ({
    label: event.name,
    value: index,
  })),
)

function handleEventChange(value: number) {
  selectedEventIndex.value = value
}

onMounted(() => {
  renderChart()
})

function renderChart() {
  if (!hasData.value) {
    // Chart is not rendered if there is no data
  }
}
</script>

<template>
  <div class="chart-container">
    <!-- Event selector dropdown -->
    <div v-if="props.eventDetail.length > 1" class="event-select-container">
      <n-select
        v-model:value="selectedEventIndex"
        :options="eventOptions"
        class="event-select"
        placeholder="Select Event"
        @update:value="handleEventChange"
      />
    </div>

    <div class="chart-wrapper">
      <!-- Render the line chart even if the data is empty -->
      <Line v-if="true" :data="chartData" :options="chartOptions" />
    </div>
  </div>
</template>

<style scoped>
.chart-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  height: 480px;
}

.event-select-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  width: 100%;
  padding-right: 20px;
}

.event-select {
  width: 200px;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
