<script setup lang="ts">
import { defineProps, ref, watch } from 'vue'
import { type ECOption, useEcharts } from '@/hooks'
import { preDefinedColors } from '@/utils/default-values'

// Define the type for the ticket data
interface TicketData {
  name: string
  total: number
  percentage: number
}

// Define the incoming data prop
const props = defineProps<{ data: TicketData[] }>()

// Predefined color scheme
const predefinedColors = preDefinedColors()

// Initialize the chart options
const option = ref<ECOption>({
  title: {
    text: 'Ticket Type',
    left: 'start',
    top: '10px',
    textStyle: {
      fontSize: 20,
      color: '#333',
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: (params) => {
      const ticket = params.data
      return `Total tickets: ${ticket.total}`
    },
  },
  legend: {
    bottom: 'bottom',
    data: [], // Legend data will be populated dynamically
  },
  toolbox: {
    show: true,
    feature: {
      mark: { show: true },
      dataView: { show: false, readOnly: false },
      restore: { show: false },
      saveAsImage: { show: true },
    },
  },
  series: [
    {
      name: 'Ticket Type',
      type: 'pie',
      radius: ['30%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: true,
        fontSize: '12px',
        color: '#333',
        position: 'outside',
        formatter: '{c}%',
      },
      labelLine: {
        show: true,
      },
      labelLayout: {
        draggable: true,
      },
      data: props.data.map((ticket, index) => ({
        value: ticket.percentage,
        name: ticket.name,
        total: ticket.total,
        itemStyle: { color: predefinedColors[index % predefinedColors.length] }, // Assign color based on index
      })),
    },
  ],
})

// Watch for changes in the prop and update the chart
watch(
  () => props.data,
  (newData) => {
    // Set the data for the chart, including the color logic
    option.value.series[0].data = newData.map((ticket, index) => ({
      value: ticket.percentage,
      name: ticket.name,
      total: ticket.total,
      itemStyle: { color: predefinedColors[index % predefinedColors.length] }, // Dynamically assign color based on index
    }))

    // Update legend data and color based on the current data
    if (newData.length > 0) {
      // Populate legend with ticket names if data exists
      option.value.legend.data = newData.map(ticket => ticket.name)
    }
    else {
      // If no data, add a placeholder item with 'No Data' and color it green
      option.value.legend.data = ['No Data']
      option.value.series[0].data = [{ value: 0, name: 'No Data', itemStyle: { color: '#eee' } }]
    }
  },
  { immediate: true }, // Update immediately when the component is mounted
)

const lineRef = ref<HTMLElement | null>(null)
useEcharts(lineRef, option)
</script>

<template>
  <div
    ref="lineRef"
    class="h-400px"
  />
</template>

<style scoped>
</style>
