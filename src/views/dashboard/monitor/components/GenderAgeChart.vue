<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface GenderAgeData {
  range: string
  totalMale: number
  totalFemale: number
}

const props = defineProps<{
  totalGenderByAgeRange: GenderAgeData[]
}>()

const chartRef = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

function getChartOption(data: GenderAgeData[]): EChartsOption {
  const processedData = data.length > 0
    ? data
    : [
        { range: 'No Data', totalMale: 0, totalFemale: 0 },
      ]

  return {
    title: {
      text: 'Age Group by Gender',
      left: '20',
      top: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const male = (params[0].value || 0) < 1 ? 0 : params[0].value
        const female = (params[1]?.value || 0) < 1 ? 0 : params[1]?.value

        return `
    <div style="padding: 3px">
      <div>Age: ${params[0].name}</div>
      <div style="display: flex; justify-content: space-between; min-width: 100px">
        <span>Male:</span>
        <span>${male}</span>
      </div>
      <div style="display: flex; justify-content: space-between; min-width: 100px">
        <span>Female:</span>
        <span>${female}</span>
      </div>
    </div>
  `
      },

    },
    grid: {
      top: 80,
      bottom: 40,
      left: '40',
      right: '30',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: processedData.map(item => item.range),
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 14,
        margin: 16,
      },
    },
    yAxis: {
      type: 'value',
      show: false,
    },
    series: [
      {
        name: 'Male',
        type: 'bar',
        data: processedData.map(item => item.totalMale > 0 ? item.totalMale : 0.1),
        itemStyle: {
          color: '#91A67E',
          borderRadius: [20, 20, 20, 20],
        },
        barWidth: 30,
        barGap: '30%',
        emphasis: {
          itemStyle: {
            color: '#7f916e',
          },
        },
      },
      {
        name: 'Female',
        type: 'bar',
        data: processedData.map(item => item.totalFemale > 0 ? item.totalFemale : 0.1),
        itemStyle: {
          color: '#B29F90',
          borderRadius: [20, 20, 20, 20],
        },
        barWidth: 30,
        emphasis: {
          itemStyle: {
            color: '#9e8d7f',
          },
        },
      },
    ],
  }
}

function initChart() {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    updateChart()
  }
}

function updateChart() {
  if (chartInstance) {
    chartInstance.setOption(getChartOption(props.totalGenderByAgeRange))
  }
}

function handleResize() {
  chartInstance?.resize()
}

watch(
  () => props.totalGenderByAgeRange,
  () => {
    if (chartInstance) {
      updateChart()
    }
    else {
      initChart()
    }
  },
  { deep: true },
)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chartInstance?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <n-card class="chart-container" style="height: 400px;">
    <div
      ref="chartRef"
      style="height: 400px;"
    />
  </n-card>
</template>

<style scoped>
.chart-container {
  width: 100%;
  max-width: 800px;
  margin: 0;
  border: unset;
}
</style>
