<script setup>
import moment from 'moment'
import { useEventDetailStore } from '@/store/event-detail'
import Lime from '@/assets/lime.svg'

const eventDetailStore = useEventDetailStore()
</script>

<template>
  <n-scrollbar content-class="px-4">
    <n-timeline class="h-400px py-4" size="large">
      <template v-if="eventDetailStore.eventDetailList.length > 0">
        <n-timeline-item
          v-for="(item, index) in eventDetailStore.eventDetailList.sort((a, b) => new Date(a.checkInStartAt) - new Date(b.checkInStartAt))"
          :key="item.id"
          type="success"
          :title="item.name"
          :time="moment(item.checkInStartAt).format('YYYY MMM DD h:mm a')"
          class="relative pb-8 ps-6"
        >
          <template #default>
            <div v-if="index === 0" class="absolute left-[-10px] top-[-10px] w-30px">
              <n-image :src="Lime" preview-disabled class="w-100% h-100%" />
            </div>
          </template>
        </n-timeline-item>
      </template>
      <template v-else>
        <n-timeline-item
          v-for="i in [0, 1, 2, 3, 4, 5, 6, 7]"
          :key="i"
          type="success"
          title=""
          time=""
          class="relative pb-8 ps-6"
        >
          <template #default>
            <div v-if="i === 0" class="absolute left-[-10px] top-[-10px] w-30px">
              <n-image :src="Lime" preview-disabled class="w-100% h-100%" />
            </div>
          </template>
        </n-timeline-item>
      </template>
    </n-timeline>
  </n-scrollbar>
</template>

<style>
.n-timeline-item-timeline__line{
  background-color: #7f9a66 !important;
}
</style>
