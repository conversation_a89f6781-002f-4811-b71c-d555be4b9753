<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui'
import { NButton, NPopconfirm, NSpace, NTooltip } from 'naive-ui'
import { ref } from 'vue'
import { RouterLink } from 'vue-router'
import TableModal from './TableModal.vue'
import SearchModal from './SearchModal.vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { useEventStore } from '@/store/event'
import { formatDate } from '@/utils'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: formModalVisible, setTrue: openFormModal } = useBoolean(false)
const { bool: searchModalVisible, setTrue: openSearchModal, setFalse: closeSearchModal } = useBoolean(false)

const eventStore = useEventStore()

const modalType = ref<ModalType>('add')

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleEdit(row: Entity.Event) {
  eventStore.editData = row
  setModalType('edit')
  openFormModal()
}

function handleAddTable() {
  setModalType('add')
  openFormModal()
}

async function fetchEventList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await eventStore.fetchEventList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handleCreate(data: Entity.Event.CreateParams) {
  await eventStore.createEvent(data)
  eventStore.handleResetSearch()
  fetchEventList(true)
}

async function handleUpdate(data: Entity.Event.UpdateParams) {
  await eventStore.updateEvent(data)
  eventStore.handleResetSearch()
  fetchEventList(true)
}

async function handleDelete(id: number) {
  await eventStore.deleteEvent(id)
  eventStore.handleResetSearch()
  fetchEventList(true)
}

async function handleCriteriaFilter() {
  fetchEventList(false)
  closeSearchModal()
}

const columns: DataTableColumns<Entity.Event> = [
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row: Entity.Event, index: number) {
      return index + 1 + ((eventStore.currentPage - 1) * eventStore.currentSize)
    },
  },
  { title: $t('event.name'), align: 'center', key: 'name' },
  { title: $t('event.description'), align: 'center', key: 'description', className: 'limit-width' },
  {
    title: $t('event.startAt'),
    align: 'center',
    key: 'startAt',
    render(row: Entity.Event) {
      return formatDate(row.startAt)
    },
  },
  {
    title: $t('event.endAt'),
    align: 'center',
    key: 'endAt',
    render(row: Entity.Event) {
      return formatDate(row.endAt)
    },
  },
  {
    title: $t('common.action'),
    align: 'center',
    key: 'actions',
    render(row: Entity.Event) {
      return (
        <NSpace justify="center">
          <NTooltip>
            {{
              default: () => $t('route.eventDetail'),
              trigger: () => (
                <RouterLink
                  to={{
                    path: 'event-details',
                    query: { eventId: row.id },
                  }}
                >
                  <NButton size="small">
                    <icon-park-outline-doc-detail />
                  </NButton>
                </RouterLink>
              ),
            }}
          </NTooltip>

          <NTooltip>
            {{
              default: () => $t('common.edit'),
              trigger: () => (
                <NButton size="small" onClick={() => handleEdit(row)}>
                  <icon-park-outline-edit-two />
                </NButton>
              ),
            }}
          </NTooltip>

          <NTooltip>
            {{
              default: () => $t('common.delete'),
              trigger: () => (
                <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
                  {{
                    default: () => $t('common.confirmDelete'),
                    trigger: () => (
                      <NButton size="small" type="error" ghost>
                        <icon-park-outline-delete />
                      </NButton>
                    ),
                  }}
                </NPopconfirm>
              ),
            }}
          </NTooltip>
        </NSpace>
      )
    },
  },
]

onMounted(async () => {
  const route = useRoute()
  eventStore.companyId = Number(route.query.companyId)
  await fetchEventList()
})
</script>

<template>
  <NSpace size="large" vertical class="flex-1">
    <n-card>
      <NSpace vertical size="large">
        <div class="flex gap-4">
          <NButton type="primary" @click="handleAddTable">
            <template #icon>
              <icon-park-outline-add-one />
            </template>
            {{ $t('common.addNew') }}
          </NButton>
          <n-input-group class="min-w-80 w-auto">
            <n-input v-model:value="eventStore.searchKeyword" :placeholder="`${$t('common.search')} Name Or Description`" clearable />
            <NButton type="primary" @click="fetchEventList(true)">
              <icon-park-outline-search />
            </NButton>
          </n-input-group>
          <NButton strong ghost class="ml-a" @click="openSearchModal">
            <template #icon>
              <icon-park-outline-filter />
            </template>
            {{ $t('common.filter') }}
          </NButton>
        </div>
        <div style="white-space: pre;" class="mt-2">
          <n-data-table :columns="columns" :data="eventStore.eventList" :loading="loading" />
        </div>
        <Pagination
          :count="eventStore.totalItems"
          :page="eventStore.currentPage"
          :page-size="eventStore.currentSize"
          @change="eventStore.handlePageChange"
        />
        <TableModal
          v-model:visible="formModalVisible"
          :type="modalType"
          :modal-data="eventStore.editData"
          :company-id="eventStore.companyId"
          :on-create="handleCreate"
          :on-update="handleUpdate"
        />
        <SearchModal
          v-model:visible="searchModalVisible"
          :type="modalType"
          :on-search="handleCriteriaFilter"
        />
      </NSpace>
    </n-card>
  </NSpace>
</template>

<style scoped>
  :deep(.limit-width){
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis
  }
</style>
