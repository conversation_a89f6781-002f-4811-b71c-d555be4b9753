<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import moment from 'moment'
import { $t } from '@/utils/i18n'
import { useEventStore } from '@/store/event'
import { toUnix } from '@/utils'

// Interface declaration
interface Props {
  visible: boolean
  type?: ModalType
  modalData?: Entity.Event | null
  companyId: number
  onCreate?: (data: Entity.Event.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.Event.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// End of Type and Interface declaration

const emit = defineEmits<Emits>()

const eventStore = useEventStore()
const formModel = ref<Nullable<Entity.Event.CreateParams>>(eventStore.defaultFormModel)
const dateRange = ref<[number, number] | null >(null)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.edit'),
}[props.type || 'add']))

function updateFormModelByModalType() {
  formModel.value.companyId = props.companyId

  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData }
    dateRange.value = [toUnix(props.modalData.startAt), toUnix(props.modalData.endAt)]
  }

  else {
    formModel.value = {
      companyId: props.companyId,
      name: null,
      description: null,
      startAt: null,
      endAt: null,
    }
    const now = new Date()

    const startOfNowPlusOneHour = new Date(now.getTime() + 1 * 60 * 60 * 1000)
    const endOfToday = new Date(now.setHours(23, 59, 59, 999))

    dateRange.value = [
      toUnix(moment(startOfNowPlusOneHour).format('YYYY-MM-DD HH:mm:ss')),
      toUnix(moment(endOfToday).format('YYYY-MM-DD HH:mm:ss')),
    ]

    formModel.value.startAt = moment(startOfNowPlusOneHour).format('YYYY-MM-DD HH:mm:ss')
    formModel.value.endAt = moment(endOfToday).format('YYYY-MM-DD HH:mm:ss')
  }
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.Event.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.Event.UpdateParams)
  }

  modalVisible.value = false
}

function onDateRangeChange(value: any) {
  if (value && value.length === 2) {
    formModel.value.startAt = moment(value[0]).format('YYYY-MM-DD HH:mm:ss')
    formModel.value.endAt = moment(value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  else {
    formModel.value.startAt = null
    formModel.value.endAt = null
  }
}

function disablePreviousDate(timestamp: number): boolean {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const todayTimestamp = today.getTime()
  return timestamp < todayTimestamp
}

watch(() => props.visible, (newValue) => {
  if (newValue)
    updateFormModelByModalType()
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="w-500px rounded-theme">
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-ticket />
              <b class="uppercase">{{ title }} Event</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            Customize your event by defining essential details ensuring a seamless experience for your participants
          </div>
        </n-flex>
      </template>
      <n-form label-placement="top" :model="formModel" label-align="left">
        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item :label="$t('event.name')" path="name">
            <n-input
              v-model:value="formModel.name" show-count
              :maxlength="150"
            />
          </n-form-item-grid-item>
          <n-form-item-grid-item :label="$t('event.description')" path="description">
            <n-input v-model:value="formModel.description" type="textarea" />
          </n-form-item-grid-item>
        </n-grid>

        <n-grid cols="1 s:2" x-gap="18" responsive="screen">
          <n-form-item-grid-item :label="$t('common.dateTimeRange')" path="dateRange" :span="2">
            <n-date-picker
              v-model:value="dateRange"
              type="datetimerange"
              clearable
              style="width: 100%"
              :is-date-disabled="disablePreviousDate"
              @update:value="onDateRangeChange"
            />
          </n-form-item-grid-item>
        </n-grid>
      </n-form>
      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
