<script setup lang="ts">
import { ref } from 'vue'
import type { FormInst, StepsProps } from 'naive-ui'

import moment from 'moment'
import { $t } from '@/utils/i18n'
import { useEventStore } from '@/store/event'
import Watermark from '@/components/common/Watermark.vue'
import { useCompanyStore } from '@/store/company'
import EventImage from '@/assets/svg/event.svg'

// End of Type and Interface declaration

const currentRef = ref<number>(1)
const currentStatus = ref<StepsProps['status']>('process')
const visible = ref(true)
const watermarkVisible = ref(true)
const formRef = ref<FormInst | null>(null)

const totalSteps = 2
const tabs = ref(['Step 1', 'Step 2'])

const value = ref(tabs.value[0])

const router = useRouter()
const eventStore = useEventStore()
const companyStore = useCompanyStore()
const formModel = ref<Nullable<Entity.Event.CreateParams>>(eventStore.defaultFormModel)
const dateRange = ref<[number, number] | null >(null)
const rules = {
  name: {
    required: true,
    message: 'Please input your name',
    trigger: 'blur',
  },
  description: {
    required: true,
    message: 'Please input a description',
    trigger: 'blur',
  },
}

async function handleSubmit() {
  const success = await formRef.value?.validate()

  if (!formModel.value.startAt || !formModel.value.endAt) {
    window.$message.error($t('common.pleaseFill'))
    return
  }

  if (!success) {
    return
  }

  formModel.value.companyId = companyStore.companyId
  await eventStore.createEvent(formModel.value as Entity.Event.CreateParams)
  eventStore.fetchEventList(true)
  window.$message.success($t('event.createSuccess'))
  router.push('/dashboard')
}

function onDateRangeChange(value: any) {
  if (value && value.length === 2) {
    formModel.value.startAt = moment(value[0]).format('YYYY-MM-DD HH:mm:ss')
    formModel.value.endAt = moment(value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  else {
    formModel.value.startAt = null
    formModel.value.endAt = null
  }
}

function disablePreviousDate(timestamp: number): boolean {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const todayTimestamp = today.getTime()
  return timestamp < todayTimestamp
}

function next() {
  if (currentRef.value < totalSteps) {
    currentRef.value++
    value.value = tabs.value[currentRef.value - 1]
  }
}

function prev() {
  if (currentRef.value > 1) {
    currentRef.value--
    value.value = tabs.value[currentRef.value - 1]
  }
}

onBeforeMount(async () => {
  await companyStore.fetchCompanyList(true)

  const exist = companyStore.companyList.find(item => item.id === Number(localStorage.getItem('companyId')))

  if (localStorage.getItem('companyId') === '0' || !exist) {
    localStorage.removeItem('companyId')
    companyStore.companyId = 0
    router.push('/select-company')
  }

  await companyStore.setCompanyIdByLocal()

  await eventStore.fetchEventList(true)
  if (eventStore.eventList.length > 0) {
    router.push('/dashboard')
  }
})
</script>

<template>
  <div class="">
    <Watermark :show-watermark="watermarkVisible" text="SOUR ems" />
    <n-modal v-model:show="visible" :mask-closable="false" :close-on-esc="false">
      <n-card class="max-w-650px p-10 min-h-650px rounded-theme">
        <n-button class="mb-8" type="primary" @click="router.push('/select-company')">
          <template #icon>
            <icon-park-outline-arrow-left />
          </template>
          Back
        </n-button>
        <n-steps :current="currentRef" :status="currentStatus">
          <n-step
            title="Craft Your Experience"
            description="Design your event with us!"
          />
          <n-step
            title="Confirm Your Event"
            description="Submit your event information to make it official!"
          />
        </n-steps>
        <n-space vertical class="max-w-500px mx-auto">
          <n-tabs v-model:value="value" tab-style="display:none !important;">
            <n-tab-pane name="Step 1">
              <n-flex class="mt-10">
                <n-flex class="w-100%">
                  <n-image :src="EventImage" class="w-40% mx-auto animate-tada" :preview-disabled="true" />
                </n-flex>
                <div class="mx-auto fw-normal text-[22px]">
                  Create your first event!
                </div>
                <p class="text-center text-gray text-[14px] mx-auto">
                  Ready to bring your event to life? In the next step, you’ll fill out the details to make it happen. Let’s get started on creating an unforgettable experience!
                </p>
              </n-flex>
            </n-tab-pane>

            <n-tab-pane name="Step 2">
              <div>
                <n-form ref="formRef" label-placement="top" :model="formModel" label-align="left" :rules="rules">
                  <n-grid cols="1" x-gap="18" responsive="screen">
                    <n-form-item-grid-item :label="$t('event.name')" path="name" feedback-class="mb-4">
                      <n-input
                        v-model:value="formModel.name" show-count
                        :maxlength="150"
                        size="large"
                      />
                    </n-form-item-grid-item>
                    <n-form-item-grid-item :label="$t('event.description')" path="description" feedback-class="mb-4">
                      <n-input
                        v-model:value="formModel.description" type="textarea"
                        size="large"
                      />
                    </n-form-item-grid-item>
                  </n-grid>

                  <n-grid cols="1 s:2" x-gap="18" responsive="screen">
                    <n-form-item-grid-item :label="$t('common.dateTimeRange')" path="dateRange" :span="2">
                      <n-date-picker
                        v-model:value="dateRange"
                        type="datetimerange"
                        clearable
                        style="width: 100%"
                        :is-date-disabled="disablePreviousDate"
                        size="large"
                        @update:value="onDateRangeChange"
                      />
                    </n-form-item-grid-item>
                  </n-grid>
                </n-form>
              </div>
            </n-tab-pane>
          </n-tabs>
        </n-space>
        <template #footer>
          <n-flex justify="center" class="mt-4">
            <n-space>
              <n-button-group>
                <template v-if="currentRef === totalSteps">
                  <n-button class="w-130px" icon-placement="left" size="large" @click="prev">
                    Previous
                    <template #icon>
                      <icon-park-outline-arrow-left />
                    </template>
                  </n-button>
                  <n-button class="w-130px" type="primary" icon-placement="right" size="large" @click="handleSubmit">
                    Complete
                    <template #icon>
                      <icon-park-outline-arrow-right />
                    </template>
                  </n-button>
                </template>
                <n-button v-else class="w-130px" type="primary" icon-placement="right" size="large" @click="next">
                  Next
                  <template #icon>
                    <icon-park-outline-arrow-right />
                  </template>
                </n-button>
              </n-button-group>
            </n-space>
          </n-flex>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<style scoped>
  .n-tabs .n-tabs-tab-wrapper {
    display: none !important;
  }

  .n-tabs-wrapper .n-tabs-tab-wrapper {
    background: red;
  }

  .animate-tada {
    animation: tada 2s infinite;
  }

  @keyframes tada {
  from {
    transform: scale3d(1, 1, 1);
  }
  10%,
  20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%,
  50%,
  70%,
  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%,
  60%,
  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
</style>
