<script setup lang="ts">
import { defineAsyncComponent, h } from 'vue'
import type { MenuOption } from 'naive-ui'
import { $t, renderIcon } from '@/utils'

const Event = defineAsyncComponent(() => import('./components/Event.vue'))

const menuOptions: MenuOption[] = [
  {
    label: () =>
      h(
        'a',
        {
          href: '#',
          rel: 'noopener noreferrer',
        },
        $t('route.event'),
      ),
    key: 'event',
    icon: renderIcon('mdi:event-note-outline'),
  },
]

const componentMap: Record<string, any> = {
  event: Event,
}
</script>

<template>
  <SubMenuContentLayout
    initial-tab="event"
    :menu-items="menuOptions"
    :components="componentMap"
  />
</template>

<style scoped></style>
