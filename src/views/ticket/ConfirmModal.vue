<script setup lang="ts">
import { computed, defineEmits } from 'vue'

// Define props interface
interface Props {
  showModal: boolean
  title?: string
  message?: string
  confirmButtonText?: string
  cancelButtonText?: string
}

// Define props using `defineProps` without passing the argument
const props = withDefaults(defineProps<Props>(), {
  confirmButtonText: 'Confirm',
  cancelButtonText: 'Cancel',
})

// Define emits
const emit = defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
  (e: 'update:showModal', value: boolean): void
}>()

// Methods to handle modal
function closeModal() {
  emit('update:showModal', false)
}

function handleConfirm() {
  emit('confirm')
  closeModal()
}

function handleCancel() {
  emit('cancel')
  closeModal()
}

// Computed property for modal visibility
const modalVisible = computed({
  get: () => props.showModal,
  set: (visible: boolean) => emit('update:showModal', visible),
})
</script>

<template>
  <n-modal v-model:show="modalVisible" style="width: 500px">
    <n-card
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
      class="rounded-theme"
    >
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <b>{{ props.title }}</b>
              <icon-park-outline-check-correct class="mb-1 text-xl" />
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="handleCancel" />
          </n-flex>
        </n-flex>
      </template>
      <div class="text-sm text-[#707070]">
        {{ props.message }}
      </div>
      <template #footer>
        <n-space justify="end">
          <n-button @click="handleCancel">
            {{ props.cancelButtonText }}
          </n-button>
          <n-button type="primary" class="bg-theme" :bordered="false" @click="handleConfirm">
            {{ props.confirmButtonText }}
          </n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
