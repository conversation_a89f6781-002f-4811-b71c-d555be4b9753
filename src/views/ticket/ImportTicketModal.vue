<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue'
import type { UploadFileInfo } from 'naive-ui'

// Interface declaration
interface Props {
  visible: boolean
  onSubmit: (fileList: UploadFileInfo[]) => Promise<void>
  loading: boolean
  importSuccess: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const fileList = ref<UploadFileInfo[]>([])

// End of Type and Interface declaration

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

watch(() => props.importSuccess, (newValue) => {
  if (newValue)
    fileList.value = []
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    title="Import Ticket"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="w-650px min-h-400px rounded-theme">
      <template #header>
        <n-flex horizontal align="center" justify="space-between">
          <n-flex horizontal align="center">
            <icon-park-outline-ticket />
            <b class="uppercase">Import Ticket</b>
          </n-flex>
          <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
        </n-flex>
      </template>

      <div>
        <b> Import Guidelines</b>
        <br>
        <i class="text-gray">During the import process, please ensure that each contact and email is unique. Entries with duplicate contact details or email addresses will be ignored to maintain data integrity.</i>
      </div>

      <n-upload
        v-model:file-list="fileList"
        directory-dnd
        :max="1"
        accept=".csv"
      >
        <n-upload-dragger class="mt-5">
          <div style="margin-bottom: 12px">
            <nova-icon icon="bi:filetype-csv" :size="75" :depth="3" />
          </div>
          <n-text style="font-size: 16px">
            Click or drag a file to this area to upload
          </n-text>
          <n-p depth="3" style="margin: 8px 0 0 0">
            Please follow the csv template given
          </n-p>
        </n-upload-dragger>
      </n-upload>

      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="onSubmit(fileList)">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
