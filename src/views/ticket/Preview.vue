<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue'
import html2pdf from 'html2pdf.js'
import { useRoute } from 'vue-router'
import { useTicketStore } from '@/store/ticket'
import { useBoolean } from '@/hooks'
import { pixelsToMm } from '@/utils'

const route = useRoute()
const ticketStore = useTicketStore()

const ticketData = ref<Entity.Ticket>()
const ticketPreviewData = ref<string>('')

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

async function convertImageToBase64(imageUrl: string): Promise<string> {
  const apiUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(imageUrl)}`
  const response = await fetch(apiUrl)

  if (response.ok) {
    const imageBlob = await response.blob()
    const reader = new FileReader()

    return new Promise((resolve, reject) => {
      reader.onloadend = () => {
        resolve(reader.result as string)
      }
      reader.onerror = reject
      reader.readAsDataURL(imageBlob)
    })
  }

  throw new Error('Failed to fetch image data.')
}

async function processTicketContent(content: string): Promise<string> {
  const regex = /<img[^>]+src="(https:\/\/[^">]+)"/g
  let processedContent = content
  let match = regex.exec(content)

  while (match !== null) {
    const imageUrl = match[1]

    try {
      const base64Url = await convertImageToBase64(imageUrl)
      processedContent = processedContent.replace(imageUrl, base64Url)
    }
    catch (error) {
      console.error('Failed to convert image to Base64:', error)
    }

    match = regex.exec(content)
  }

  return processedContent
}

// Function to download PDF
async function downloadPDF() {
  const element = document.getElementById('ticket-content')
  if (element) {
    // Get the width and height of the element
    const rect = element.getBoundingClientRect()

    // Convert the width and height from pixels to millimeters
    const widthMm = pixelsToMm(rect.width, 96) + 10
    const heightMm = pixelsToMm(rect.height, 96) + 15

    const options = {
      margin: [5, 5, 5, 5],
      filename: `${ticketData.value?.companyName} - ${ticketData.value?.holderName} - ${ticketData.value?.email}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 3, useCORS: true },
      jsPDF: { unit: 'mm', format: [widthMm, heightMm], orientation: 'portrait' },
    }

    html2pdf().from(element).set(options).save()
  }
}

onMounted(async () => {
  let ticketNumber = ''
  if (route.query.ticketNumber) {
    ticketNumber = route.query.ticketNumber.toString()
  }
  startLoading()

  const data = await ticketStore.ticketPreview(ticketNumber)

  ticketPreviewData.value = await processTicketContent(data.template)
  ticketData.value = data.ticket

  nextTick(() => {
    if (ticketPreviewData.value.trim() !== '') {
      endLoading()
    }
  })
})
</script>

<template>
  <div class="mx-auto pb-10">
    <n-button primary type="primary" size="large" class="my-4" :loading="loading" :disabled="loading" @click="downloadPDF">
      Download as PDF
    </n-button>
    <div id="ticket-content" v-html="ticketPreviewData" />
  </div>
</template>
