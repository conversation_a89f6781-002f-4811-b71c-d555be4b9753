<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import { isEmpty } from 'radash'
import { $t } from '@/utils/i18n'
import { useTicketStore } from '@/store/ticket'
import { useTicketTypeStore } from '@/store/ticket-type'
import { useMaritalStatusStore } from '@/store/marital-status'

// Interface declaration
interface Props {
  visible: boolean
  type?: ModalType
  modalData?: Entity.Ticket | null
  onCreate?: (data: Entity.Ticket.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.Ticket.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// etc

const emit = defineEmits<Emits>()
// End of Type and Interface declaration

// stores
const ticketStore = useTicketStore()
const ticketTypeStore = useTicketTypeStore()
const maritalStatusStore = useMaritalStatusStore()

const formModel = ref<Nullable<Entity.Ticket.CreateParams>>(ticketStore.defaultFormModel)
const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.edit'),
}[props.type || 'add']))

function updateFormModelByModalType() {
  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData }
  }
  else {
    formModel.value = {
      holderName: null,
      companyName: null,
      email: null,
      eventId: null,
      ticketTypeId: null,
      contactNumber: null,
      customNote: [],

      // to be adjust
      gender: 'M',
      maritalStatusId: 1,
      age: null,
    }
  }
}

const genderOptions = [
  {
    label: 'Male',
    value: 'M',
  },
  {
    label: 'Female',
    value: 'F',
  },
]

async function handleSubmit() {
  if (formModel.value.customNote && !isEmpty(formModel.value.customNote) && formModel.value.customNote.every(note => note.key.trim() === '' || note.value.trim() === '')) {
    window.$message.error('Please fill in all the custom notes')
    return
  }

  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.Ticket.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.Ticket.UpdateParams)
  }

  modalVisible.value = false
}

const ticketTypeOptions = computed(() => {
  return ticketTypeStore.ticketTypeList.map(ticketType => ({ label: ticketType.name, value: ticketType.id }))
})

function addCustomNote() {
  if (formModel.value.customNote) {
    formModel.value.customNote.push({ key: '', value: '' })
  }
  else {
    formModel.value.customNote = [{ key: '', value: '' }]
  }
}

function removeCustomNote(index: number) {
  if (formModel.value.customNote) {
    formModel.value.customNote.splice(index, 1)
  }
}

function onMaritalStatusChange(newMaritalStatusId: number) {
  const maritalStatus = maritalStatusStore.getMaritalStatusById(newMaritalStatusId)
  formModel.value.maritalStatusId = maritalStatus !== null ? maritalStatus.id : 0
}

onBeforeMount(() => {
  maritalStatusStore.maritalStatusList()
})

watch(() => props.visible, (newValue) => {
  if (newValue)
    updateFormModelByModalType()
  else
    ticketStore.fetchTicketList()
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    :title="title"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="w-500px rounded-theme min-h-750px">
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-ticket />
              <b class="uppercase">{{ title }} Ticket</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            For new attendees, allowing you to specify event and manage ticket availability for a seamless registration process.
          </div>
        </n-flex>
      </template>

      <n-tabs type="segment" animated>
        <n-tab-pane name="ticket details" tab="Ticket Details">
          <n-form label-placement="top" :model="formModel" label-align="left">
            <n-grid cols="1" x-gap="18" responsive="screen">
              <n-form-item-grid-item :label="$t('ticket.name')" path="holderName">
                <n-input
                  v-model:value="formModel.holderName" show-count
                  :maxlength="25"
                />
              </n-form-item-grid-item>

              <n-form-item-grid-item :label="$t('ticket.email')" path="email">
                <n-input v-model:value="formModel.email" />
              </n-form-item-grid-item>

              <n-form-item-grid-item :label="$t('ticket.contactNumber')" path="contactNumber">
                <n-input
                  v-model:value="formModel.contactNumber" class="flex-1" show-count
                  :maxlength="12"
                />
              </n-form-item-grid-item>

              <n-form-item-grid-item :label="$t('ticket.companyName')" path="companyName">
                <n-input
                  v-model:value="formModel.companyName" show-count
                  :maxlength="100"
                />
              </n-form-item-grid-item>
            </n-grid>

            <n-grid cols="3" x-gap="18" responsive="screen">
              <n-form-item-grid-item label="Age" path="age">
                <n-input-number
                  v-model:value="formModel.age"
                  class="w-100%"
                  :min="1"
                  :max="100"
                  placeholder="Input"
                />
              </n-form-item-grid-item>

              <n-form-item-grid-item label="Gender" path="gender">
                <n-select
                  v-model:value="formModel.gender"
                  filterable
                  :placeholder="$t('common.select')"
                  :options="genderOptions"
                />
              </n-form-item-grid-item>

              <n-form-item-grid-item label="Marital Status" path="maritalStatusId">
                <n-select
                  v-model:value="formModel.maritalStatusId"
                  filterable
                  :placeholder="$t('common.select')"
                  :options="maritalStatusStore.maritalStatusOptions"
                  @update:value="onMaritalStatusChange"
                />
              </n-form-item-grid-item>
            </n-grid>

            <n-grid cols="1" responsive="screen">
              <n-form-item-grid-item :label="$t('ticket.ticketType')" path="ticketTypeId">
                <n-select
                  v-model:value="formModel.ticketTypeId"
                  filterable
                  :placeholder="$t('common.select')"
                  :options="ticketTypeOptions"
                />
              </n-form-item-grid-item>
            </n-grid>
          </n-form>
        </n-tab-pane>

        <n-tab-pane name="custom notes" tab="Custom Notes">
          <div class="flex justify-end ">
            <n-button type="success" size="small" ghost class="mb-4" @click="addCustomNote">
              <template #icon>
                <icon-park-outline-add-one />
              </template>
              Add New
            </n-button>
          </div>

          <n-scrollbar class="max-h-450px">
            <template v-for="(note, index) in formModel.customNote" :key="index">
              <n-grid cols="1" x-gap="18" responsive="screen">
                <n-form-item-grid-item :path="`customNote[${index}].key`">
                  <template #label>
                    {{ $t('ticket.customKey') }}
                    <NButton size="tiny" type="error" ghost class="ms-2" @click="removeCustomNote(index)">
                      <template #icon>
                        <icon-park-outline-delete />
                      </template>
                    </NButton>
                  </template>
                  <n-input v-model:value="note.key" placeholder="Enter key" />
                </n-form-item-grid-item>

                <n-form-item-grid-item :label="$t('ticket.customValue')" :path="`customNote[${index}].value`">
                  <n-input v-model:value="note.value" placeholder="Enter value" type="textarea" :resizable="false" />
                </n-form-item-grid-item>
              </n-grid>
            </template>
          </n-scrollbar>
        </n-tab-pane>
      </n-tabs>

      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
