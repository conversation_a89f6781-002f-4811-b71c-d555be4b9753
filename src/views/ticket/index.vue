<script setup lang="tsx">
import type { DataTableColumns, DataTableInst, DataTableSortState, UploadFileInfo } from 'naive-ui'
import { NButton, NPopconfirm, NSpace } from 'naive-ui'
import { ref } from 'vue'
import html2pdf from 'html2pdf.js'
import { Icon } from '@iconify/vue'
import TicketType from '../ticketType/index.vue'
import SelectedDataModal from '../ticketRegistrationApproval/SelectedDataModal.vue'
import TableModal from './TableModal.vue'
import SearchModal from './SearchModal.vue'
import ConfirmModal from './ConfirmModal.vue'
import ImportTicketModal from './ImportTicketModal.vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { useTicketStore } from '@/store/ticket'
import { useEventDetailStore } from '@/store/event-detail'
import { useEventStore } from '@/store/event'
import { renderIcon } from '@/utils'
import { getSortKey } from '@/utils/default-values'

// loading states
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: importLoading, setTrue: startImportLoading, setFalse: endImportLoading } = useBoolean(false)
const { bool: exportLoading, setTrue: startExportLoading, setFalse: endExportLoading } = useBoolean(false)
const { bool: importSuccess, setTrue: setImportSuccess, setFalse: setImportUnsuccess } = useBoolean(false)
const { bool: ticketDownloading, setTrue: startTicketDownloading, setFalse: endTicketDownloading } = useBoolean(false)

// visibility states
const { bool: formModalVisible, setTrue: openFormModal } = useBoolean(false)
const { bool: selectedDataModalVisible, setTrue: openSelectedDataModal, setFalse: closeSelectedDataModal } = useBoolean(false)
const { bool: searchModalVisible, setTrue: openSearchModal, setFalse: closeSearchModal } = useBoolean(false)
const { bool: importModalVisible, setTrue: openImportModal, setFalse: closeImportModal } = useBoolean(false)
const { bool: ticketDrawerVisible, setTrue: openTicketDrawer, setFalse: closeTicketDrawer } = useBoolean(false)
const { bool: confirmModalVisible, setTrue: openConfirmModal, setFalse: closeConfirmModal } = useBoolean(false)

// stores
const eventStore = useEventStore()
const ticketStore = useTicketStore()
const eventDetailStore = useEventDetailStore()

// etc

const confirmModalTitle = ref<string>('No action')
const eventId = ref<number>(0)
const tableRef = ref<DataTableInst>()
const modalType = ref<ModalType>('add')
const selectedRowsData = ref<Entity.Ticket[]>([])
const checkedRowKeysRef = ref<Array<string | number>>([])
const action = ref<(() => void)>(() => window.$message.warning('No action selected'))

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleEdit(row: Entity.Ticket | null) {
  ticketStore.editData = row
  setModalType('edit')
  openFormModal()
}

function handleAddTable() {
  setModalType('add')
  openFormModal()
}

function rowProps(row: Entity.Ticket) {
  return {
    style: 'cursor: pointer;',
    onClick: (e: MouseEvent) => {
      // Prevent the drawer from opening when the checkbox is clicked
      const target = e.target as HTMLElement
      if (target.closest('.n-checkbox')) {
        return
      }

      openTicketDrawer()
      ticketStore.editData = row
    },
  }
}

const router = useRouter()

function openInNewTab(ticketNumber) {
  const routeData = router.resolve({
    path: 'ticket-preview',
    query: { ticketNumber },
  })

  window.open(routeData.href, '_blank')
}

async function fetchTicketList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await ticketStore.fetchTicketList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handleSorterUpdate(options: DataTableSortState | DataTableSortState[] | null) {
  if (!options)
    return

  const isArray = Array.isArray(options)
  if (!isArray && !options.order) {
    await fetchTicketList(true)
    return
  }

  ticketStore.sort = isArray
    ? options.map(option => ({ column: option.columnKey.toString(), order: getSortKey(option.order) }))
    : [{ column: options.columnKey.toString(), order: getSortKey(options.order) }]

  await fetchTicketList(false)
}

async function handleClearSort() {
  tableRef.value?.clearSorter()
  await fetchTicketList(true)
}

async function handleCreate(data: Entity.Ticket.CreateParams) {
  await ticketStore.createTicket(data)
  ticketStore.handleResetSearch()
  fetchTicketList(true)
}

async function handleUpdate(data: Entity.Ticket.UpdateParams) {
  await ticketStore.updateTicket(data)
  ticketStore.handleResetSearch()
  await fetchTicketList()
  ticketStore.editData = ticketStore.ticketList.find(t => t.id === data.id) || null
}

async function handleDelete(id: number) {
  await ticketStore.deleteTicket(id)
  ticketStore.handleResetSearch()
  closeTicketDrawer()
  fetchTicketList(true)
}

async function handleCriteriaFilter() {
  fetchTicketList(false)
  closeSearchModal()
}

async function handlePageChange(page: number, size: number) {
  startLoading()
  await ticketStore.handlePageChange(page, size)
  endLoading()
}

async function convertImageToBase64(imageUrl: string): Promise<string> {
  const apiUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(imageUrl)}`
  const response = await fetch(apiUrl)

  if (response.ok) {
    const imageBlob = await response.blob()
    const reader = new FileReader()

    return new Promise((resolve, reject) => {
      reader.onloadend = () => {
        resolve(reader.result as string)
      }
      reader.onerror = reject
      reader.readAsDataURL(imageBlob)
    })
  }

  throw new Error('Failed to fetch image data.')
}

async function processTicketContent(content: string): Promise<string> {
  const regex = /<img[^>]+src="(https:\/\/[^">]+)"/g
  let processedContent = content
  let match = regex.exec(content)

  while (match !== null) {
    const imageUrl = match[1]

    try {
      const base64Url = await convertImageToBase64(imageUrl)
      processedContent = processedContent.replace(imageUrl, base64Url)
    }
    catch (error) {
      console.error('Failed to convert image to Base64:', error)
    }

    match = regex.exec(content)
  }

  return processedContent
}

async function downloadTicket() {
  const selectedTickets = checkedRowKeysRef.value
  if (!selectedTickets.length) {
    window.$message.warning('Please select at least one ticket.')
    return
  }

  startTicketDownloading()
  for (const ticketId of selectedTickets) {
    const ticket = ticketStore.ticketList.find(t => t.id === ticketId)

    if (ticket) {
      const processedTemplate = replaceConstantsInTemplate(ticket)
      const processedContent = await processTicketContent(processedTemplate || '')
      const element = document.createElement('div')
      element.innerHTML = processedContent

      const options = {
        margin: [5, 5, 5, 5],
        filename: `${ticket?.companyName} - ${ticket?.holderName} - ${ticket?.email}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'mm', orientation: 'portrait', format: [115, 195] },
      }

      await html2pdf().from(element).set(options).save()
    }
  }
  endTicketDownloading()
}

async function handleImport(fileList: UploadFileInfo[]) {
  try {
    if (fileList.length === 0) {
      window.$message.error('File is required!')
      return
    }

    const file = fileList[0].file
    if (!file) {
      return
    }

    startImportLoading()
    const success = await ticketStore.importTicket(ticketStore.eventId, file)
    if (success) {
      setImportSuccess()
      window.$message.success('Import successfully!')
      fetchTicketList(true)
    }
    else {
      setImportUnsuccess()
    }

    closeImportModal()
    endImportLoading()
  }
  catch (e) {
    endImportLoading()
    return e
  }
}

async function exportTicket() {
  startExportLoading()
  const url = await ticketStore.exportTicket(checkedRowKeysRef.value)
  endExportLoading()

  if (!url) {
    window.$message.error('Failed to fetch the CSV url')
    return
  }
  window.location.href = url
}

function replaceConstantsInTemplate(ticket: Entity.Ticket) {
  const Constants = {
    TICKET_PICTURE: '{PICTURE_BASE64}',
    TICKET_CODE: '{CODE_BASE64}',
    TICKET_SHOW_TICKET_LINK: '{SHOW_TICKET_LINK}',
    TICKET_ENCODED_TICKET_NUMBER: '{ENCODED_TICKET_NUMBER}',
    TICKET_EVENT_NAME: '{EVENT_NAME}',
    TICKET_EVENT_DATE_TIME: '{EVENT_DATE_TIME}',
    TICKET_HOLDER_NAME: '{HOLDER_NAME}',
    TICKET_TABLE_NAME: '{TABLE_NAME}',
    TICKET_INSTRUCTIONS: '{INSTRUCTIONS}',
    TICKET_SOUR_EMS_LOGO: '{SOUR_EMS_LOGO}',
    TICKET_DISPLAY_TABLE_BUTTON: '{TICKET_DISPLAY_TABLE_BUTTON}',
  }
  let template = ticketStore.template

  template = template?.replace(`${Constants.TICKET_CODE}`, ticket.qrCode || '')
  template = template?.replace(`${Constants.TICKET_HOLDER_NAME}`, ticket.holderName)
  template = template?.replace(`${Constants.TICKET_TABLE_NAME}`, ticket.tableSeat?.tableInfo.name || '---')
  template = template?.replace(new RegExp(Constants.TICKET_ENCODED_TICKET_NUMBER, 'g'), ticket.encryptedTicketNumber || '')
  template = template?.replace(`${Constants.TICKET_DISPLAY_TABLE_BUTTON}`, ticket.tableSeats.length > 1 ? 'none' : 'block')

  return template
}

const dropdownOptions = computed(() => {
  return [
    {
      key: 'downloadTicket',
      label: 'Download Ticket',
      icon: renderIcon('icon-park-outline:file-pdf-one'),
    },
    {
      key: 'import',
      label: 'Import',
      icon: renderIcon('uil:import'),
    },
    {
      key: 'export',
      label: 'Export',
      icon: renderIcon('uil:export'),
    },
    {
      key: 'clearSort',
      label: 'Clear Sort',
      icon: renderIcon('mdi:sort-variant-remove'),
    },
  ]
})

const columns: DataTableColumns<Entity.Ticket> = [
  {
    type: 'selection',
    options: [
      'all',
      'none',
    ],
  },
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row: Entity.Ticket, index: number) {
      return index + 1 + ((ticketStore.currentPage - 1) * ticketStore.currentSize)
    },
  },
  { title: $t('ticket.ticketNumber'), align: 'center', key: 'ticketNumber' },
  { title: $t('ticket.name'), align: 'center', key: 'holderName', sorter: true },
  { title: $t('ticket.email'), align: 'center', key: 'email', className: 'max-w-200px overflow-ellipsis', sorter: true },
  { title: $t('ticket.companyName'), align: 'center', key: 'companyName', className: 'max-w-200px overflow-ellipsis', sorter: true },
  { title: $t('ticket.contactNumber'), align: 'center', key: 'contactNumber' },
  {
    title: $t('ticket.ticketType'),
    align: 'center',
    key: 'ticketTypeId',
    sorter: true,
    render(row: Entity.Ticket) {
      return row.ticketType?.name || 'Unknown'
    },
  },
  { title: $t('ticket.totalEmailSent'), align: 'center', key: 'totalEmailSent', sorter: true },
]

function handleDropdownSelect(key: string | number) {
  if (key === 'downloadTicket') {
    confirmModalTitle.value = 'Download Ticket'
    action.value = () => {
      downloadTicket()
      closeSelectedDataModal()
    }
    openSelectedDataModal()
  }
  else if (key === 'import') {
    openImportModal()
  }
  else if (key === 'export') {
    exportTicket()
  }
  else if (key === 'clearSort') {
    handleClearSort()
  }
}

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0) {
    endLoading()
    return
  }

  ticketStore.eventId = eventId.value
  ticketStore.searchKeyword = null
  await fetchTicketList()
  await eventDetailStore.fetchEventDetailList()
  endLoading()
})

watch(checkedRowKeysRef, (newCheckedKeys) => {
  newCheckedKeys.forEach((key) => {
    const row = ticketStore.ticketList.find(item => item.id === key)
    if (row && !selectedRowsData.value.some(item => item.id === row.id)) {
      selectedRowsData.value.push(row)
    }
  })

  selectedRowsData.value = selectedRowsData.value.filter(row => newCheckedKeys.includes(row.id))
})

onBeforeMount(() => {
  startLoading()
  eventId.value = eventStore.selectedEvent?.id || 0
  ticketStore.handleResetSearch()
})
</script>

<template>
  <NSpace size="large" vertical class="flex-1" style="gap:44px">
    <TicketType />

    <n-card :border="false" class="rounded-theme shadow min-h-[calc(100vh-1000px)]">
      <NSpace vertical size="large">
        <div class="flex flex-wrap gap-4 mb-2">
          <NButton type="default" @click="handleAddTable">
            <template #icon>
              <icon-park-outline-add-one />
            </template>
            Add New
          </NButton>

          <n-input-group class="sm:w-80">
            <n-input v-model:value="ticketStore.searchKeyword" placeholder="Search Keyword" clearable @keyup.enter="fetchTicketList(true)">
              <template #prefix>
                <div>
                  <icon-park-outline-search />
                </div>
              </template>
            </n-input>
          </n-input-group>

          <NDropdown trigger="click" :options="dropdownOptions" placement="bottom-start" @select="handleDropdownSelect">
            <NButton ghost :loading="ticketDownloading || importLoading || exportLoading">
              <template #icon>
                <icon-park-outline-more-one />
              </template>
              Actions
            </NButton>
          </NDropdown>

          <NButton ghost class="ml-a" @click="openSearchModal">
            <template #icon>
              <icon-park-outline-filter />
            </template>
            Filter
          </NButton>

          <NButton ghost @click="openSelectedDataModal">
            <template #icon>
              <Icon icon="icon-park-outline:list" />
            </template>
            Show Selected Data
          </NButton>
        </div>

        <div style="white-space: pre;" class="min-h-[400px]">
          <n-data-table
            ref="tableRef"
            v-model:checked-row-keys="checkedRowKeysRef"
            :columns="columns"
            :data="ticketStore.ticketList"
            :loading="loading"
            :bordered="false"
            class="shadow rounded-theme"
            :row-props="rowProps" :row-key="(row) => row.id"
            @update:sorter="handleSorterUpdate"
          />
          <div id="ticket-template" class="hidden" v-html="ticketStore.template" />
        </div>
      </NSpace>

      <div class="flex flex-wrap justify-between">
        <Pagination
          class="mt-4"
          :count="ticketStore.totalItems"
          :page="ticketStore.currentPage"
          :page-size="ticketStore.currentSize"
          @change="handlePageChange"
        />

        <div class="text-gray-400 mt-4">
          {{ ticketStore.totalItems }} Ticket(s)
        </div>
      </div>

      <TableModal
        v-model:visible="formModalVisible"
        :type="modalType"
        :modal-data="ticketStore.editData"
        :on-create="handleCreate"
        :on-update="handleUpdate"
      />

      <SearchModal
        v-model:visible="searchModalVisible"
        :on-search="handleCriteriaFilter"
      />

      <ImportTicketModal
        v-model:visible="importModalVisible"
        :on-submit="handleImport"
        :loading="importLoading"
        :import-success="importSuccess"
      />

      <ConfirmModal
        :show-modal="confirmModalVisible"
        :title="confirmModalTitle"
        message="Are you sure the result in the listing are correctly filtered?"
        confirm-button-text="Yes, Proceed!"
        cancel-button-text="Cancel"
        @confirm="action"
        @cancel="closeConfirmModal"
        @update:show-modal="val => confirmModalVisible = val"
      />

      <SelectedDataModal
        :columns="columns.filter((_, index) => ![0, 1].includes(index))"
        :show-modal="selectedDataModalVisible"
        :selected-rows-data="selectedRowsData"
        :action="openConfirmModal"
        @update:show-modal="val => selectedDataModalVisible = val"
      />

      <n-drawer v-model:show="ticketDrawerVisible" :width="500" class="custom-drawer">
        <n-drawer-content class="">
          <div class="flex flex-col h-full">
            <div class="pt-4 px-4">
              <n-flex class="my-4" align="center" justify="space-between">
                <span class="text-2xl fw-bold">
                  {{ ticketStore.editData?.holderName }}
                </span>
                <div class="ms-auto flex flex-row gap-x-2">
                  <NButton type="default" size="small" @click="openInNewTab(ticketStore.editData?.ticketNumber)">
                    <icon-park-outline-preview-open />
                  </NButton>
                  <NButton type="default" size="small" @click="handleEdit(ticketStore.editData)">
                    <icon-park-outline-edit-two />
                  </NButton>
                  <NPopconfirm @positive-click="handleDelete(Number(ticketStore.editData?.id))">
                    Confirm Delete?
                    <template #trigger>
                      <NButton size="small" type="error" ghost>
                        <icon-park-outline-delete />
                      </NButton>
                    </template>
                  </NPopconfirm>
                </div>
              </n-flex>
            </div>

            <n-divider class="px-4" />

            <n-flex vertical class="p-4">
              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Ticket Number:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.ticketNumber }}
                </div>
              </n-flex>

              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Email:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.email }}
                </div>
              </n-flex>

              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Age:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.age }}
                </div>
              </n-flex>

              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Company Name:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.companyName }}
                </div>
              </n-flex>

              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Contact Number:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.contactNumber }}
                </div>
              </n-flex>

              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Gender:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.gender }}
                </div>
              </n-flex>

              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Total Email Sent:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.totalEmailSent }}
                </div>
              </n-flex>

              <n-flex align="center" class="space-x-4 mb-4">
                <div class="min-w-40">
                  Ticket Type:
                </div>
                <div class="font-bold">
                  {{ ticketStore.editData?.ticketType?.name }}
                </div>
              </n-flex>
            </n-flex>

            <div class="flex-1 bg-[#f2f2f2] mt-4 p-4 pt-8" style="border-top-left-radius: 20px; border-top-right-radius: 20px">
              <div class="text-2xl fw-bold">
                Custom Note
              </div>

              <div class="mt-4">
                <template v-if="(ticketStore.editData?.customNote?.length || 0) > 0">
                  <div v-for="(note, index) in ticketStore.editData?.customNote" :key="index">
                    <div class="flex space-x-4 mb-4">
                      <div class="min-w-40">
                        {{ note.key }}
                      </div>
                      <div class="font-bold">
                        {{ note.value }}
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <span><strong>N/A</strong></span>
                </template>
              </div>
            </div>
          </div>
        </n-drawer-content>
      </n-drawer>
    </n-card>
  </NSpace>
</template>

<style>
.custom-drawer .n-drawer-body-content-wrapper{
  padding:0 !important;
}
</style>
