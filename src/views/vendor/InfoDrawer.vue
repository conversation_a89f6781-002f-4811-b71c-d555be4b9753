<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import type { UploadFileInfo } from 'naive-ui'
import TableModal from './TableModal.vue'
import UploadFileModal from './components/UploadFileModal.vue'
import ProfileInformation from './components/ProfileInformation.vue'
import AttendeeTracking from './components/AttendeeTracking.vue'
import { useBoolean } from '@/hooks'
import { useSponsorStore } from '@/store/sponsor'
import { useSponsorPackageStore } from '@/store/sponsor-package'
import { useEventStore } from '@/store/event'

// Interface & Type declaration
interface Props {
  color: string
  visible: boolean
  modalData?: Entity.Sponsor | null
  onUpdate?: (data: Entity.Sponsor.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

type Tab = 'profile-details' | 'file-system' | 'attendee-tracking'

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// store
const eventStore = useEventStore()
const sponsorStore = useSponsorStore()
const sponsorpackageStore = useSponsorPackageStore()

// visibility states
const { bool: submitLoading, setTrue: startSubmitLoading, setFalse: endSubmitLoading } = useBoolean(false)
const { bool: editModalVisible, setTrue: openEditModal, setFalse: closeEditModal } = useBoolean(false)

// etc
const eventId = ref<number>(0)
const dataChanged = ref<boolean>(false)
const fileList = ref<UploadFileInfo[]>([])
const formModel = ref<Nullable<Entity.Sponsor.CreateParams>>(sponsorStore.defaultFormModel)
const selectedTab = ref<Tab>('profile-details')
const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

function updateFormModelByModalType() {
  formModel.value = {
    eventId: null,
    sponsorPackageId: null,
    name: null,
    picName: null,
    picEmail: null,
    picContactNumber: null,
    amountReceived: null,
    address: null,
    customNote: null,
    picPosition: null,
  }
}

function handleEdit() {
  openEditModal()
}

function changeTab(tab: Tab) {
  selectedTab.value = tab
}

// function formatFileSize(sizeInBytes) {
//   if (!sizeInBytes || sizeInBytes <= 0)
//     return '0 B'

//   const units = ['B', 'KB', 'MB', 'GB', 'TB']
//   let size = Number.parseFloat(sizeInBytes)
//   let unitIndex = 0

//   while (size >= 1024 && unitIndex < units.length - 1) {
//     size /= 1024
//     unitIndex++
//   }

//   // Format to 2 decimal places if not a whole number, otherwise no decimals
//   const formattedSize = size % 1 === 0 ? size.toFixed(0) : size.toFixed(2)
//   return `${formattedSize} ${units[unitIndex]}`
// }

async function handleUpdate(data: Entity.Sponsor.UpdateParams) {
  startSubmitLoading()
  await sponsorStore.updateSponsor(data)
  await sponsorStore.fetchSponsorList(true)
  await sponsorStore.fetchVendorDashboard()
  window.$message.success('Sponsor updated successfully!')
  endSubmitLoading()
  modalVisible.value = false
  closeEditModal()
}

async function handleDelete(id: any) {
  await sponsorStore.deleteSponsor(id)
  await sponsorStore.fetchSponsorList(true)
  await sponsorStore.fetchVendorDashboard()
  window.$message.success('Sponsor deleted successfully!')
  modalVisible.value = false
}

onMounted(async () => {
  eventId.value = Number(eventStore.selectedEvent?.id)
})

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0 || Number.isNaN(newValue)) {
    return
  }
  sponsorpackageStore.eventId = eventId.value
  await sponsorpackageStore.fetchSponsorPackageList()
})

watch(() => props.visible, async (newValue) => {
  fileList.value = []
  if (!newValue)
    return

  updateFormModelByModalType()
  const files: File[] = await sponsorStore.fetchSponsorFiles(props.modalData?.id || 0)
  fileList.value = files.map(file => ({
    id: file.fileName,
    name: file.fileName,
    url: file.fileUrl,
    file,
    status: 'finished' as const,
  }))
})

watch(dataChanged, async (newVal) => {
  if (newVal === false) {
    return
  }

  await nextTick()

  fileList.value = []
  const files: File[] = await sponsorStore.fetchSponsorFiles(props.modalData?.id || 0)
  fileList.value = files.map(file => ({
    id: file.fileName,
    name: file.fileName,
    url: file.fileUrl,
    file,
    status: 'finished' as const,
  }))

  dataChanged.value = false
})
</script>

<template>
  <div>
    <n-drawer
      v-model:show="modalVisible"
      :mask-closable="true"
      class="!w-80% !sm:w-70% "
      close-on-esc
      style="border-top-left-radius: 25px !important;border-bottom-left-radius: 25px !important;"
    >
      <n-drawer-content>
        <n-card class="rounded-theme vendor-card">
          <div class="flex flex-col lg:flex-row">
            <div class="flex flex-col lg:flex-row flex-1 lg:items-center gap-4">
              <div class="w-120px h-120px rounded-full flex justify-center items-center" :style="{ backgroundColor: color }">
                <span class="text-3xl text-white fw-bold">{{ modalData?.name[0] }}</span>
              </div>

              <div class="flex flex-col mt-4 lg:mt-0">
                <div class="flex flex-col lg:flex-row fw-bold flex-1 lg:items-center gap-4">
                  <n-ellipsis class="text-2xl ">
                    {{ modalData?.name }}
                  </n-ellipsis>
                  <div v-if="(modalData?.amountReceived || 0) / (modalData?.sponsorPackage?.amountSponsored || 1) < 1" class="bg-[#FEF2F2] text-[#9B4E48] py-2 rounded-2xl fw-bold w-140px text-center">
                    PENDING
                  </div>
                  <div v-else class="bg-[#F3FDF8] text-[#448469] py-2 rounded-2xl fw-bold w-140px text-center">
                    PAID
                  </div>
                </div>

                <span class="text-muted mt-4 lg:mt-1 mb-2 text-lg">
                  <n-ellipsis>
                    {{ modalData?.sponsorPackage?.name }}
                  </n-ellipsis>
                </span>
              </div>
            </div>
          </div>
        </n-card>

        <div class="flex flex-col xl:flex-row min-h-80% gap-6">
          <n-card class="rounded-theme mt-6 xl:mt-8 w-full xl:w-250px xl:min-w-250px vendor-card" content-class="flex flex-col !p-2">
            <div class="w-full flex flex-col gap-y-2">
              <div class="fw-semibold text-[#77976A] cursor-pointer px-6 py-2 rounded-xl text-lg" :class="[selectedTab === 'profile-details' ? 'bg-[#7F9A6620]' : '']" @click="changeTab('profile-details')">
                Profile Details
              </div>
              <div class="fw-semibold text-[#77976A] cursor-pointer px-6 py-2 rounded-xl text-lg" :class="[selectedTab === 'file-system' ? 'bg-[#7F9A6620]' : '']" @click="changeTab('file-system')">
                File System
              </div>
              <div class="fw-semibold text-[#77976A] cursor-pointer px-6 py-2 rounded-xl text-lg" :class="[selectedTab === 'attendee-tracking' ? 'bg-[#7F9A6620]' : '']" @click="changeTab('attendee-tracking')">
                Attendee Tracking
              </div>
            </div>
          </n-card>

          <div class="flex flex-col flex-1 gap-y-6 xl:max-w-[calc(100%-275px)]">
            <template v-if="selectedTab === 'profile-details'">
              <ProfileInformation :modal-data="modalData" :handle-edit="handleEdit" :handle-delete="() => handleDelete(modalData?.id)" />
            </template>

            <template v-else-if="selectedTab === 'file-system'">
              <n-card class="rounded-theme xl:mt-8 flex-1 vendor-card" content-class="flex flex-col">
                <UploadFileModal v-model:data-changed="dataChanged" :modal-data="props.modalData" />
              </n-card>
            </template>

            <template v-else-if="selectedTab === 'attendee-tracking'">
              <AttendeeTracking :modal-data="modalData" :handle-edit="handleEdit" :handle-delete="() => handleDelete(modalData?.id)" />
            </template>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>

    <TableModal v-model:visible="editModalVisible" :modal-data="props.modalData" type="edit" :on-update="handleUpdate" :loading="submitLoading" />
  </div>
</template>

<style scoped>
</style>

<style>
.n-progress .n-progress-graph .n-progress-graph-line.n-progress-graph-line--indicator-inside .n-progress-graph-line-rail .n-progress-graph-line-indicator{
  text-align: center !important;
  font-weight: 700 !important;
}
</style>
