<script setup lang="tsx">
import { NSpace } from 'naive-ui'
import TableModal from './components/TableModal.vue'
import { useBoolean } from '@/hooks'
import Lime from '@/assets/lime.svg'
import YellowLime from '@/assets/yellow-lime.svg'
import OrangeLime from '@/assets/orange-lime.svg'
import { useSponsorPackageStore } from '@/store/sponsor-package'
import { useEventStore } from '@/store/event'
import { useAppStore } from '@/store'
import SourClick from '@/assets/sour-click.svg'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: addModalVisible, setTrue: openModal } = useBoolean(false)
const { bool: drawerVisible, setTrue: openDrawer, setFalse: closeDrawer } = useBoolean(false)

const appStore = useAppStore()
const eventId = ref<number>(0)
const sponsorPackageStore = useSponsorPackageStore()
const eventStore = useEventStore()

const modalType = ref<ModalType>('add')

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleDrawer(row: Entity.SponsorPackage) {
  sponsorPackageStore.editData = row
  openDrawer()
}

function handleEdit() {
  setModalType('edit')
  openModal()
}

function handleAddTable() {
  setModalType('add')
  openModal()
}

async function fetchSponsorPackageList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await sponsorPackageStore.fetchSponsorPackageList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handleCreate(data: Entity.SponsorPackage.CreateParams) {
  await sponsorPackageStore.createSponsorPackage(data)
  sponsorPackageStore.handleResetSearch()
  fetchSponsorPackageList(true)
}

async function handleUpdate(data: Entity.SponsorPackage.UpdateParams) {
  await sponsorPackageStore.updateSponsorPackage(data)
  sponsorPackageStore.handleResetSearch()
  fetchSponsorPackageList(true)
  closeDrawer()
}

async function handleDelete(id: number) {
  await sponsorPackageStore.deleteSponsorPackage(id)
  // sponsorPackageStore.handleResetSearch()
  // fetchSponsorPackageList(true)
  appStore.reloadPage()
}

onBeforeMount(() => {
  eventId.value = Number(eventStore.selectedEvent?.id) || 0
})

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0) {
    return
  }
  sponsorPackageStore.eventId = eventId.value
  await fetchSponsorPackageList()
})
</script>

<template>
  <NSpace vertical size="large" class="flex-1">
    <div class="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-8" style="gap: 30px !important;">
      <template v-if="!loading">
        <template v-if="sponsorPackageStore.sponsorPackageList.length > 0">
          <div v-for="(item, index) in sponsorPackageStore.sponsorPackageList" :key="item.id" class="w-full">
            <n-card class="shadow-md rounded-theme w-full h-full py-1 relative min-h-[300px] hover:cursor-pointer" @click="handleDrawer(item)">
              <n-image
                v-if="(index + 1) % 3 === 1"
                :src="YellowLime"
                class="absolute top-0 left-[-15px] w-[30px] object-cover rotate-[340deg]"
                preview-disabled
              />
              <n-image
                v-else-if="(index + 1) % 3 === 2"
                :src="Lime"
                class="absolute top-0 left-[-15px] w-[30px] object-cover rotate-[340deg]"
                preview-disabled
              />
              <n-image
                v-else-if="(index + 1) % 3 === 0"
                :src="OrangeLime"
                class="absolute top-0 left-[-15px] w-[30px] object-cover rotate-[340deg]"
                preview-disabled
              />

              <n-flex vertical>
                <div class="flex flex-col sm:flex-row justify-between sm:items-center">
                  <div class="fw-bold text-3xl flex-1 overflow-hidden text-ellipsis">
                    <n-ellipsis>
                      {{ item.name }}
                    </n-ellipsis>
                  </div>
                  <div class="text-xl fw-semibold" style="min-width: fit-content; white-space: nowrap;">
                    ${{ Number(item.amountSponsored).toLocaleString() }}
                  </div>
                </div>
                <n-divider style="margin:0;" />
                <n-flex vertical class="text-base">
                  <n-flex horizontal justify="space-between" class="mt-4">
                    <div class="text-muted">
                      Speaking Slots
                    </div>
                    <div class="fw-medium">
                      {{ item.speakingSlotDuration }}min
                    </div>
                  </n-flex>

                  <n-flex horizontal justify="space-between" class="mt-4">
                    <div class="text-muted">
                      Ticket(s)
                    </div>
                    <div class="fw-medium">
                      {{ item.totalTickets }}
                    </div>
                  </n-flex>

                  <n-flex horizontal justify="space-between" class="mt-4">
                    <div class="text-muted">
                      Booth(s)
                    </div>
                    <div class="fw-medium">
                      {{ item.totalBooths }}
                    </div>
                  </n-flex>

                  <n-flex horizontal justify="space-between" class="mt-4">
                    <div class="text-muted">
                      Table(s)
                    </div>
                    <div class="fw-medium">
                      {{ item.totalTables }}
                    </div>
                  </n-flex>

                  <div class="mt-8 text-base text-muted">
                    <n-ellipsis :line-clamp="6" :tooltip="false">
                      {{ item.customNote }}
                    </n-ellipsis>
                  </div>
                </n-flex>
              </n-flex>
            </n-card>
          </div>
        </template>

        <div class="w-full">
          <n-card class="rounded-theme shadow h-full min-h-[300px] cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition bg-[#fff] relative" content-class="flex items-center justify-center" @click="handleAddTable">
            <n-flex vertical align="center" class="text-center">
              <n-flex class="mx-auto" align="center">
                <img :src="SourClick" alt="" class="h-[60px] opacity-[0.7] object-contain">
              </n-flex>
              <h2 class="text-gray-800 font-bold text-4xl mt-8 mb-2 text-success opacity-[0.7] lh-[45px]">
                NEW VENDOR <br>PACKAGE
              </h2>
            </n-flex>
          </n-card>
        </div>
      </template>

      <template v-else>
        <div v-for="i in [0, 1, 2]" :key="i" class="w-full">
          <n-card class="shadow-md rounded-theme w-full py-1 relative h-[300px] hover:cursor-pointer">
            <n-skeleton :repeat="10" class="w-full mb-3" />
          </n-card>
        </div>
      </template>
    </div>

    <n-drawer
      v-model:show="drawerVisible"
      :mask-closable="true"
      :width="550"
      :segmented="{ content: true, action: true }"
      :show-header="true"
      close-on-esc
      style="border-top-left-radius: 20px; border-bottom-left-radius: 20px"
      class="custom-drawer !max-w-80% sm:max-w-90%"
    >
      <n-drawer-content>
        <n-flex vertical class="p-4">
          <n-flex class="my-4 " align="center" justify="space-between">
            <div>
              <div class="text-2xl fw-bold">
                {{ sponsorPackageStore.editData?.name }}
              </div>
              <div class="text-xl fw-semibold">
                ${{ Number(sponsorPackageStore.editData?.amountSponsored).toLocaleString() }}
              </div>
            </div>
            <div class="ms-auto flex flex-row gap-x-2">
              <NButton type="default" size="small" @click="handleEdit()">
                <icon-park-outline-edit-two />
              </NButton>
              <NPopconfirm @positive-click="handleDelete(Number(sponsorPackageStore.editData?.id))">
                Confirm Delete?
                <template #trigger>
                  <NButton size="small" type="error" ghost>
                    <icon-park-outline-delete />
                  </NButton>
                </template>
              </NPopconfirm>
            </div>
          </n-flex>

          <n-divider style="margin:0;" />

          <n-flex vertical class="text-base">
            <n-flex class="mt-4">
              <div v-for="item in sponsorPackageStore.editData?.sponsors" :key="item.id" class="bg-white rounded-full shadow px-4 py-2 text-[#448469] fw-bold text-sm">
                {{ item.name }}
              </div>
            </n-flex>

            <n-flex horizontal justify="space-between" class="mt-4">
              <div class="text-muted">
                Speaking Slots
              </div>
              <div class="fw-medium">
                {{ sponsorPackageStore.editData?.speakingSlotDuration }}min
              </div>
            </n-flex>

            <n-flex horizontal justify="space-between" class="mt-4">
              <div class="text-muted">
                Ticket(s)
              </div>
              <div class="fw-medium">
                {{ sponsorPackageStore.editData?.totalTickets }}
              </div>
            </n-flex>

            <n-flex horizontal justify="space-between" class="mt-4">
              <div class="text-muted">
                Booth(s)
              </div>
              <div class="fw-medium">
                {{ sponsorPackageStore.editData?.totalBooths }}
              </div>
            </n-flex>

            <n-flex horizontal justify="space-between" class="mt-4">
              <div class="text-muted">
                Table(s)
              </div>
              <div class="fw-medium">
                {{ sponsorPackageStore.editData?.totalTables }}
              </div>
            </n-flex>

            <div class="mt-8 text-base text-black">
              {{ sponsorPackageStore.editData?.customNote }}
            </div>
          </n-flex>
        </n-flex>
      </n-drawer-content>
    </n-drawer>

    <TableModal
      v-model:visible="addModalVisible"
      :type="modalType"
      :modal-data="sponsorPackageStore.editData"
      :on-create="handleCreate"
      :on-update="handleUpdate"
    />
  </NSpace>
</template>
