<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import { useSponsorPackageStore } from '@/store/sponsor-package'

// Interface declaration
interface Props {
  visible: boolean
  type?: ModalType
  modalData?: Entity.SponsorPackage | null
  onCreate?: (data: Entity.SponsorPackage.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.SponsorPackage.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// End of Type and Interface declaration

const emit = defineEmits<Emits>()

const sponsorPackageStore = useSponsorPackageStore()
const formModel = ref<Nullable<Entity.SponsorPackage.CreateParams>>(sponsorPackageStore.defaultFormModel)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: 'Add',
  edit: 'Edit',
}[props.type || 'add']))

const route = useRoute()

function updateFormModelByModalType() {
  formModel.value.eventId = Number(route.query.eventId)
  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData }
    formModel.value.amountSponsored = Number(formModel.value.amountSponsored)
  }

  else {
    formModel.value = {
      eventId: sponsorPackageStore.eventId,
      name: null,
      amountSponsored: null,
      customNote: null,
      sortOrder: null,
      speakingSlotDuration: null,
      totalBooths: null,
      totalTables: null,
      totalTickets: null,
    }
  }
}

function handleInput(value) {
  formModel.value.name = value.toUpperCase()
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.SponsorPackage.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.SponsorPackage.UpdateParams)
  }

  modalVisible.value = false
}

watch(() => props.visible, (newValue) => {
  if (newValue)
    updateFormModelByModalType()
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    class="w-500px"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="rounded-theme">
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-ranking />
              <b class="uppercase">{{ title }} VENDOR PACKAGE</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            Create a vendor package by defining tiers, pricing, and benefits to offer sponsors tailored exposure and exclusive opportunities during your event.
          </div>
        </n-flex>
      </template>
      <n-form label-placement="top" :model="formModel" label-align="left">
        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item label="Name" path="name">
            <n-input
              v-model:value="formModel.name" show-count
              :maxlength="25"
              @update:value="handleInput"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Amount Sponsored" path="name">
            <n-input-number
              v-model:value="formModel.amountSponsored"
              :show-button="false"
              class="w-full" show-count
              :min="0"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Custom Note" path="name">
            <n-input
              v-model:value="formModel.customNote"
              :show-button="false"
              class="w-full" show-count
              type="textarea"
              :rows="3"
            />
          </n-form-item-grid-item>
        </n-grid>

        <n-grid cols="2" x-gap="18" responsive="screen">
          <n-form-item-grid-item label="Speaking Slot Duration" path="name">
            <n-input-number
              v-model:value="formModel.speakingSlotDuration"
              show-count
              :min="1"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Total Tickets" path="name">
            <n-input-number
              v-model:value="formModel.totalTickets"
              class="w-full"
              show-count
              :min="0"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Total Booths" path="name">
            <n-input-number
              v-model:value="formModel.totalBooths"
              class="w-full"
              show-count
              :min="0"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Total Tables" path="name">
            <n-input-number
              v-model:value="formModel.totalTables"
              class="w-full"
              show-count
              :min="0"
            />
          </n-form-item-grid-item>
        </n-grid>

        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item label="Sorting" path="name">
            <n-input-number
              v-model:value="formModel.sortOrder"
              :show-button="false"
              class="w-full"
              show-count
              :min="1"
            />
          </n-form-item-grid-item>
        </n-grid>
      </n-form>

      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
