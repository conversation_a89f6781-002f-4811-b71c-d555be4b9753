<script setup lang="tsx">
import { NButton, NSpace } from 'naive-ui'
import { ref } from 'vue'
import { Icon } from '@iconify/vue'
import TableModal from './TableModal.vue'
import VendorDashboard from './VendorDashboard.vue'
import InfoDrawer from './InfoDrawer.vue'
import Skeleton from './components/Skeleton.vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { useEventDetailStore } from '@/store/event-detail'
import { useEventStore } from '@/store/event'
import { useSponsorStore } from '@/store/sponsor'
import { preDefinedColors } from '@/utils/default-values'

// store
const eventStore = useEventStore()
const sponsorStore = useSponsorStore()
const eventDetailStore = useEventDetailStore()
const predefinedColors = preDefinedColors()

// visibility states
const { bool: drawerVisible, setTrue: openDrawer } = useBoolean(false)
const { bool: formModalVisible, setTrue: openFormModal } = useBoolean(false)

// loading states
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: submitLoading, setTrue: startSubmitLoading, setFalse: endSubmitLoading } = useBoolean(false)
const { bool: exportLoading, setTrue: startExportLoading, setFalse: endExportLoading } = useBoolean(false)

// etc
const selectedColor = ref<string>('')
const modalType = ref<ModalType>('add')

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleAddTable() {
  setModalType('add')
  openFormModal()
}

function handleDrawer(row: Entity.Sponsor, color: string) {
  selectedColor.value = color
  sponsorStore.editData = row
  openDrawer()
}

async function fetchSponsorList(reset: boolean = false) {
  window.$loadingBar.start()
  await sponsorStore.fetchSponsorList(reset)
  window.$loadingBar.finish()
}

async function handleCreate(data: Entity.Sponsor.CreateParams) {
  startSubmitLoading()
  await sponsorStore.createSponsor(data)
  sponsorStore.handleResetSearch()
  fetchSponsorList(true)
  endSubmitLoading()
}

async function exportVendor() {
  startExportLoading()
  const url = await sponsorStore.exportSponsors()
  endExportLoading()

  if (!url) {
    window.$message.error('Failed to fetch the CSV url')
    return
  }
  window.location.href = url
}

onBeforeMount(() => {
  sponsorStore.eventId = eventStore.selectedEvent?.id || 0
})

watch(() => sponsorStore.eventId, async (newValue) => {
  if (newValue === 0 || Number.isNaN(newValue)) {
    return
  }

  startLoading()
  await fetchSponsorList()
  await eventDetailStore.fetchEventDetailList()
  endLoading()
})

onMounted(async () => {
  // reset keyword & criteria search
  sponsorStore.searchKeyword = null
})
</script>

<template>
  <NSpace size="large" vertical class="flex-1" style="gap:44px">
    <VendorDashboard />

    <n-card v-if="!loading" :border="false" class="rounded-theme shadow min-h-200px">
      <NSpace vertical size="large">
        <div class="flex flex-wrap gap-4">
          <NButton type="default" @click="handleAddTable">
            <template #icon>
              <icon-park-outline-add-one />
            </template>
            {{ $t('common.addNew') }}
          </NButton>

          <n-input-group class="sm:w-80">
            <n-input v-model:value="sponsorStore.searchKeyword" :placeholder="`${$t('common.search')} ${$t('common.keyword')}`" clearable @keyup.enter="fetchSponsorList(true)">
              <template #prefix>
                <div>
                  <icon-park-outline-search />
                </div>
              </template>
            </n-input>
          </n-input-group>

          <NButton strong ghost :loading="exportLoading" @click="exportVendor">
            <template #icon>
              <Icon icon="uil:export" />
            </template>
            Export
          </NButton>
        </div>
      </NSpace>

      <NSpace vertical size="large" class="mt-4">
        <div v-for="(item, index) in sponsorStore.sponsorList" :key="item.id" class="bg-[#669A6C33] rounded-theme p-4 mb-4 shadow hover:cursor-pointer" @click="handleDrawer(item, predefinedColors[index % predefinedColors.length])">
          <div class="flex flex-col xl:flex-row gap-6 items-center">
            <div class="w-full xl:w-65% flex flex-col lg:flex-row lg:items-center gap-x-4">
              <div class="w-75px h-75px rounded-full flex justify-center items-center" :style="{ backgroundColor: predefinedColors[index % predefinedColors.length] }">
                <span class="text-3xl text-white fw-bold">{{ item.name[0] }}</span>
              </div>

              <div class="flex flex-col mt-8 lg:mt-0">
                <span class="text-xl fw-bold flex-1">
                  <n-ellipsis class="w-120px">
                    {{ item.name }}
                  </n-ellipsis>
                </span>

                <span class="text-muted mt-1 mb-2">
                  <n-ellipsis class="lg:w-120px">
                    {{ item.sponsorPackage?.name }}
                  </n-ellipsis>
                </span>
              </div>

              <n-divider vertical class="divider-vertical-custom" />
              <n-divider class="divider-custom lg:hidden" />

              <div class="lg:ps-4 flex flex-col">
                <span class="text-muted mt-1 mb-2">
                  CONTACTS
                </span>

                <span class="text-lg fw-semibold flex-1 underline">
                  {{ item.picEmail }}
                </span>
              </div>
            </div>

            <div class="w-full xl:w-35% flex md:flex-row flex-col gap-y-4 gap-x-12">
              <div class="flex flex-col">
                <span class="text-muted mt-1 mb-2">
                  TOTAL AMOUNT
                </span>

                <span class="text-xl fw-bold flex-1">
                  ${{ Number(item.sponsorPackage?.amountSponsored).toLocaleString() }}
                </span>
              </div>

              <div class="flex flex-col">
                <span class="text-muted mt-1 mb-2">
                  COLLECTED AMOUNT
                </span>

                <span class="text-xl fw-bold flex-1">
                  ${{ Number(item.amountReceived).toLocaleString() }}
                </span>
              </div>
            </div>
          </div>

          <div class="flex flex-col xl:flex-row mb-4 mt-8 gap-6 items-center">
            <div class="w-full xl:w-65%">
              <n-progress
                :border-radius="10"
                :height="25"
                color="#7f9a66"
                type="line"
                :percentage="Math.round(((item.amountReceived || 0) / (item.sponsorPackage?.amountSponsored || 0)) * 100 * 100) / 100 || 0"
                indicator-placement="inside"
                processing
                rail-color="#fff"
              />
            </div>

            <div class="w-full xl:w-35% flex">
              <div v-if="(item.amountReceived / (item.sponsorPackage?.amountSponsored || 0)) < 1" class="bg-[#FEF2F2] text-[#9B4E48] py-2 rounded-2xl fw-bold w-140px text-center">
                PENDING
              </div>
              <div v-else class="bg-[#F3FDF8] text-[#448469] py-2 rounded-2xl fw-bold w-140px text-center">
                PAID
              </div>
            </div>
          </div>
        </div>
      </NSpace>
    </n-card>

    <Skeleton v-else />

  <InfoDrawer v-model:visible="drawerVisible" :modal-data="sponsorStore.editData" :color="selectedColor" />

    <TableModal
      v-model:visible="formModalVisible"
      :type="modalType"
      :modal-data="sponsorStore.editData"
      :on-create="handleCreate"
      :loading="submitLoading"
    />
  </NSpace>
</template>

<style>
.divider-custom.n-divider:not(.n-divider--dashed) .n-divider__line {
  background-color: #00000050 !important;
}

.divider-vertical-custom.n-divider.n-divider--vertical{
  height: 4rem;
  background-color: #00000050 !important;
}

.divider-vertical-custom.divider-vertical-custom.n-divider.n-divider--vertical {
  display: none !important;
}

@media (min-width: 1024px) {
  .divider-vertical-custom.divider-vertical-custom.n-divider.n-divider--vertical {
    display: block !important;
  }
}
</style>
