<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import { preDefinedColors } from '@/utils/default-values'

const props = defineProps<{
  sponsorPackages: SponsorLevel[]
}>()

interface SponsorLevel {
  name: string
  total: number
}

const colorScheme = preDefinedColors()
const chartRef = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

function getChartOption(data: SponsorLevel[]): EChartsOption {
  const chartData = data.map(item => ({
    name: item.name,
    value: item.total,
  }))

  return {
    title: {
      text: 'Sponsor Levels',
      left: 'start',
      top: '10px',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
    },
    legend: {
      bottom: 'bottom',
    },
    color: colorScheme,
    series: [
      {
        type: 'pie',
        radius: ['30%', '50%'],
        data: chartData,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 1,
        },
        labelLine: {
          show: true,
        },
        labelLayout: {
          draggable: true,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }
}

function initChart() {
  if (chartRef.value && props.sponsorPackages.length > 0) {
    chartInstance = echarts.init(chartRef.value)
    chartInstance.setOption(getChartOption(props.sponsorPackages))
  }
}

function updateChart() {
  if (chartInstance && props.sponsorPackages.length > 0) {
    chartInstance.setOption(getChartOption(props.sponsorPackages))
  }
}

function handleResize() {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(
  () => props.sponsorPackages,
  () => {
    if (chartInstance) {
      updateChart()
    }
    else {
      initChart()
    }
  },
  { deep: true },
)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div
    v-if="sponsorPackages.length > 0"
    ref="chartRef"
    class="h-400px"
  />
  <n-empty
    v-else
    description="No data available"
  />
</template>

<style scoped>
.chart-container {
  width: 100%;
  max-width: 800px;
  margin: 0;
  border: unset;
}
</style>
