<script setup lang="ts">
import axios from 'axios'
import type { UploadFileInfo } from 'naive-ui'
import { Icon } from '@iconify/vue'
import { useSponsorStore } from '@/store/sponsor'

// Interface declaration
interface Props {
  dataChanged?: boolean
  modalData?: Entity.Sponsor | null
}

interface Emits {
  (e: 'update:data-changed', dataChanged?: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const sponsorStore = useSponsorStore()

const uploadProgress = ref<number>(0)
const fileList = ref<UploadFileInfo[]>([])

const dataChanged = computed({
  get: () => props.dataChanged,
  set: dataChanged => emit('update:data-changed', dataChanged),
})

onMounted(async () => {
  fileList.value = []
  const files: File[] = await sponsorStore.fetchSponsorFiles(props.modalData?.id || 0)
  fileList.value = files.map(file => ({
    id: file.fileName,
    name: file.fileName,
    url: file.fileUrl,
    file,
    status: 'finished' as const,
  }))
})

async function handleChange({ fileList: newFileList }: { fileList: UploadFileInfo[] }) {
  const addedFiles = newFileList.filter(file => !fileList.value.some(f => f.id === file.id))
  const removedFiles = fileList.value.filter(file => !newFileList.some(f => f.id === file.id))

  if (addedFiles.length) {
    const success = await handleAddFile(addedFiles)
    if (success)
      window.$message.success(`File has been uploaded successfully: ${addedFiles.map((item) => { return item.name })}`)
  }

  if (removedFiles.length) {
    await handleRemoveFile(removedFiles)
    window.$message.success(`File has been removed successfully: ${removedFiles.map((item) => { return item.name })}`)
  }

  dataChanged.value = true
  fileList.value = newFileList
}

async function handleAddFile(files: UploadFileInfo[]): Promise<any> {
  let flag = true

  for (const file of files) {
    const fileName = file.name.split('.')
    try {
      const url = await sponsorStore.uploadRequest(
        props.modalData?.eventId || 0,
        props.modalData?.id || 0,
        fileName[0],
        file.type || '',
      )

      await axios.put(url, file.file, {
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total,
            )
            uploadProgress.value = percentCompleted
          }
        },
      })
    }
    catch (error) {
      fileList.value.pop()
      flag = false
      break
    }
  }

  return flag
}

async function handleRemoveFile(files: UploadFileInfo[]) {
  let flag = false
  files.map(async (file) => {
    flag = await sponsorStore.deleteFile(props.modalData?.id || 0, file.name)
  })
  return flag
}
</script>

<template>
  <div>
    <n-flex vertical>
      <n-flex horizontal align="center" justify="space-between">
        <n-flex horizontal align="center" class="text-2xl">
          <icon-park-outline-ranking />
          Upload File
        </n-flex>
      </n-flex>
      <div class="text-sm text-[#707070]">
        Upload your files for placement in key promotional materials, including the Advertisement Page in the e-Program Book, logo display on our website, and your company logo featured across all marketing collaterals.
      </div>
    </n-flex>

    <n-grid cols="1" x-gap="18" responsive="screen" class="mt-6">
      <n-grid-item label="Name" path="name">
        <n-upload
          :file-list="fileList"
          list-type="image"
          :max="3"
          show-download-button
          directory-dnd
          @change="handleChange"
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <Icon icon="ph:archive-tray-thin" size="30" class="mx-auto text-[#707070]" />
            </div>
            <n-text style="font-size: 16px">
              Click or drag a file to this area to upload
            </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0">
              Strictly prohibit from uploading sensitive information. For example,
              your bank card PIN or your credit card expiry date.
            </n-p>
            <n-progress
              type="line"
              :percentage="uploadProgress"
              :show-indicator="true"
              class="mt-4"
            />
          </n-upload-dragger>
        </n-upload>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<style scoped></style>
