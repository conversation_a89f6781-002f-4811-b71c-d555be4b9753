<script setup lang="tsx">
import { Icon } from '@iconify/vue'
import type { DataTableColumns } from 'naive-ui'
import { useBoolean } from '@/hooks'
import { useSponsorParticipantStore } from '@/store/sponsor-participant'
import { $t } from '@/utils'

interface Props {
  modalData?: Entity.Sponsor | null
  handleEdit: () => void
  handleDelete: (modalId: any) => void
}

const props = defineProps<Props>()
const sponsorParticipantStore = useSponsorParticipantStore()

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

const columns: DataTableColumns<Entity.Ticket> = [
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row: Entity.Ticket, index: number) {
      return index + 1 + ((sponsorParticipantStore.currentPage - 1) * sponsorParticipantStore.currentSize)
    },
  },
  { title: $t('ticket.ticketNumber'), align: 'center', key: 'ticketNumber' },
  { title: $t('ticket.name'), align: 'center', key: 'holderName' },
  { title: $t('ticket.email'), align: 'center', key: 'email', className: 'max-w-200px overflow-ellipsis' },
  { title: $t('ticket.companyName'), align: 'center', key: 'companyName', className: 'max-w-200px overflow-ellipsis' },
  { title: $t('ticket.contactNumber'), align: 'center', key: 'contactNumber' },
  {
    title: $t('ticket.ticketType'),
    align: 'center',
    key: 'ticketTypeId',
    render(row: Entity.Ticket) {
      return row.ticketType?.name || 'Unknown'
    },
  },
]

const landingUrl = import.meta.env.VITE_LANDING_URL
const text = ref<string>(`${landingUrl}/attendee-tracking?sponsorId=${props.modalData?.id}&password=${props.modalData?.password}&eventId=${props.modalData?.eventId}`)

function handleCopy() {
  window.$message.success('Copied to clipboard')
  navigator.clipboard.writeText(text.value)
}

async function fetchSponsorParticipantList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await sponsorParticipantStore.fetchSponsorParticipantList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handlePageChange(page: number, size: number) {
  startLoading()
  await sponsorParticipantStore.handlePageChange(page, size)
  endLoading()
}

watch(() => sponsorParticipantStore.sponsorId, async (newValue) => {
  if (newValue === 0) {
    endLoading()
    return
  }
  await fetchSponsorParticipantList(true)
  endLoading()
})

onBeforeMount(async () => {
  sponsorParticipantStore.sponsorId = props.modalData?.id || 0
})
</script>

<template>
  <n-card
    class="rounded-theme xl:mt-8 vendor-card"
    content-class="flex flex-col space-y-6"
  >
    <div class="flex flex-wrap justify-between items-center">
      <div class="flex items-center text-2xl gap-x-2">
        <Icon icon="icon-park-outline:scanning-two" class="text-3xl" />
        <span>Attendee Tracking</span>
      </div>
    </div>

    <div class="flex flex-col xl:flex-row gap-8">
      <div class="flex flex-col flex-1 space-y-6">
        <p class="text-lg leading-relaxed">
          Start tracking who has attended your booth with Attendee Tracking. Send
          this QR code or the link below to your booth managers so they can
          access your vendor’s attendee tracker and start scanning QR codes!
        </p>

        <div class="flex flex-col space-y-2">
          <div class="text-muted text-lg">
            Attendee Tracking Link
          </div>
          <div class="flex cursor-pointer" @click="handleCopy">
            <button
              class="overflow-hidden text-lg text-start break-all max-h-28px bg-transparent hover:text-[#707070] transition-all"
            >
              {{ text }}
            </button>
            <icon-park-outline:copy class="ml-2 text-xl flex-shrink-0" />
          </div>
          <copy-text v-model:value="text" />
        </div>
      </div>

      <div class="flex justify-center items-center xl:w-[200px] xl:h-[200px]">
        <n-qr-code
          class="w-[200px] h-[200px]"
          :value="text"
          error-correction-level="H"
          :size="200"
        />
      </div>
    </div>
  </n-card>

  <n-card class="flex-1 rounded-theme vendor-card min-h-400px" content-class="flex flex-col">
    <div class="flex flex-wrap gap-4">
      <n-input-group class="sm:min-w-80 w-auto">
        <n-input
          v-model:value="sponsorParticipantStore.searchKeyword"
          :placeholder="`${$t('common.search')} ${$t('common.keyword')}`"
          clearable
          @keyup.enter="fetchSponsorParticipantList(true)"
        >
          <template #prefix>
            <icon-park-outline-search />
          </template>
        </n-input>
      </n-input-group>
    </div>

    <div class="flex-1 flex flex-col justify-between">
      <div style="white-space: pre;" class="mt-4">
        <n-data-table
          :loading="loading"
          :columns="columns"
          :data="sponsorParticipantStore.sponsorParticipantList"
          class="shadow rounded-theme"
        />
      </div>

      <div class="flex justify-between mt-4 flex-wrap space-y-2">
        <Pagination
          :count="sponsorParticipantStore.totalItems"
          :page="sponsorParticipantStore.currentPage"
          :page-size="sponsorParticipantStore.currentSize"
          @change="handlePageChange"
        />
        <div class="text-gray-400">
          {{ sponsorParticipantStore.totalItems }} Ticket(s)
        </div>
      </div>
    </div>
  </n-card>
</template>

<style scoped>
.n-qr-code{
  padding:0 !important;
}
</style>
