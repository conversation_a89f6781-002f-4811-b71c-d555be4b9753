<script setup lang="tsx">
import { Icon } from '@iconify/vue'
import YellowLime from '@/assets/yellow-lime.svg'

interface Props {
  modalData?: Entity.Sponsor | null
  handleEdit: () => void
  handleDelete: (modalId: any) => void
}

const props = defineProps<Props>()
</script>

<template>
  <n-card class="rounded-theme xl:mt-8 vendor-card" content-class="flex flex-col">
    <div class="flex flex-wrap justify-between gap-y-4">
      <n-flex horizontal align="center" class="text-2xl">
        <Icon icon="streamline:user-identifier-card" class="text-3xl" />
        Profile Information
      </n-flex>

      <div class="flex gap-4">
        <NButton
          size="small"
          @click="handleEdit()"
        >
          <icon-park-outline-edit-two />
        </NButton>

        <NPopconfirm @positive-click="() => handleDelete(props.modalData?.id)">
          <template #trigger>
            <NButton size="small" type="error" ghost>
              <icon-park-outline-delete />
            </NButton>
          </template>
          Confirm Delete?
        </NPopconfirm>
      </div>
    </div>

    <n-grid cols="4" item-responsive responsive="screen" :x-gap="100" class="pt-8">
      <n-grid-item span="4 l:2">
        <div class="flex flex-col gap-y-6 mt-8 lg:mt-0">
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Name
            </div>
            <div class="fw-500">
              {{ props.modalData?.picName }}
            </div>
          </div>
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Phone Number
            </div>
            <div class="fw-500">
              {{ props.modalData?.picContactNumber }}
            </div>
          </div>
        </div>
      </n-grid-item>
      <n-grid-item span="4 l:2">
        <div class="flex flex-col gap-y-6 mt-8 lg:mt-0">
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Email
            </div>
            <div class="fw-500">
              {{ props.modalData?.picEmail }}
            </div>
          </div>
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Position
            </div>
            <div class="fw-500">
              {{ props.modalData?.picPosition }}
            </div>
          </div>
        </div>
      </n-grid-item>
    </n-grid>

    <div class="flex flex-col text-lg  mt-8">
      <div class="text-muted">
        Address
      </div>
      <div class="fw-500">
        {{ props.modalData?.address }}
      </div>
    </div>
  </n-card>

  <n-card class="flex-1 rounded-theme vendor-card">
    <div class="flex lg:flex-row flex-col items-start sm:items-center">
      <div class="flex w-full flex-1">
        <div class="relative flex-1">
          <n-progress
            class="flex-1 shadow"
            :border-radius="10"
            :height="30"
            color="#7f9a66"
            type="line"
            :percentage="Math.round(((props.modalData?.amountReceived || 0) / (props.modalData?.sponsorPackage?.amountSponsored || 0)) * 100 * 100) / 100 || 0"
            indicator-placement="inside"
            processing
            rail-color="#F2F2F2"
          />
        </div>

        <div class="w-45px h-45px relative">
          <n-image :src="YellowLime" class="w-full object-cover rotate-210 absolute left-[-20px]" preview-disabled />
        </div>
      </div>

      <div class="text-muted text-lg">
        ${{ Number(props.modalData?.amountReceived).toLocaleString() }}
      </div>
    </div>

    <n-grid cols="4" item-responsive responsive="screen" :x-gap="100" class="pt-8">
      <n-grid-item span="4 l:2">
        <div class="flex flex-col gap-y-6">
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Package
            </div>
            <div class="fw-500">
              {{ props.modalData?.sponsorPackage?.name }}
            </div>
          </div>
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Paid Amount
            </div>
            <div class="fw-500">
              ${{ Number(props.modalData?.amountReceived).toLocaleString() }}
            </div>
          </div>
        </div>
      </n-grid-item>

      <n-grid-item span="4 l:2">
        <div class="flex flex-col gap-y-6 mt-8 lg:mt-0">
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Sponsorship Amount
            </div>
            <div class="fw-500">
              ${{ Number(props.modalData?.sponsorPackage?.amountSponsored).toLocaleString() }}
            </div>
          </div>
          <div class="flex flex-col text-lg">
            <div class="text-muted">
              Pending Amount
            </div>
            <div class="fw-500">
              ${{ Number((props.modalData?.sponsorPackage?.amountSponsored ?? 0) - (props.modalData?.amountReceived ?? 0)).toLocaleString() }}
            </div>
          </div>
        </div>
      </n-grid-item>
    </n-grid>

    <div class="flex flex-col text-lg mt-6">
      <div class="text-muted">
        Custom Note
      </div>
      <div class="fw-500">
        {{ props.modalData?.customNote }}
      </div>
    </div>
  </n-card>
</template>
