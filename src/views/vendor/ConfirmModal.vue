<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ConfirmationModal',
  props: {
    showModal: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: 'Confirm Action',
    },
    message: {
      type: String,
      default: 'Are you sure you want to proceed?',
    },
    confirmButtonText: {
      type: String,
      default: 'Confirm',
    },
    cancelButtonText: {
      type: String,
      default: 'Cancel',
    },
  },
  emits: ['confirm', 'cancel', 'update:showModal'],
  setup(_, { emit }) {
    const closeModal = () => {
      emit('update:showModal', false)
    }

    const handleConfirm = () => {
      emit('confirm')
      closeModal()
    }

    const handleCancel = () => {
      emit('cancel')
      closeModal()
    }

    return {
      closeModal,
      handleConfirm,
      handleCancel,
    }
  },
})
</script>

<template>
  <n-modal v-model:show="$props.showModal">
    <n-card
      style="width: 600px"
      :title="title"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div>{{ message }}</div>

      <template #footer>
        <n-space justify="end">
          <n-button @click="handleCancel">
            {{ cancelButtonText }}
          </n-button>
          <n-button type="primary" @click="handleConfirm">
            {{ confirmButtonText }}
          </n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>
