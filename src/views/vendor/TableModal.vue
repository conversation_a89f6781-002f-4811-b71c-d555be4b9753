<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import { useSponsorPackageStore } from '@/store/sponsor-package'
import { useSponsorStore } from '@/store/sponsor'
import { useEventStore } from '@/store/event'

// Interface declaration
interface Props {
  visible: boolean
  loading?: boolean
  type?: ModalType
  modalData?: Entity.Sponsor | null
  onCreate?: (data: Entity.Sponsor.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.Sponsor.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// End of Type and Interface declaration

const emit = defineEmits<Emits>()

const eventId = ref()
const eventStore = useEventStore()
const sponsorStore = useSponsorStore()
const sponsorPackageStore = useSponsorPackageStore()
const formModel = ref<Nullable<Entity.Sponsor.CreateParams>>(sponsorStore.defaultFormModel)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

function updateFormModelByModalType() {
  formModel.value.eventId = eventStore.selectedEvent?.id || 0

  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData, sponsorPackageId: Number(props.modalData.sponsorPackage?.id) }
  }

  else {
    formModel.value = {
      eventId: null,
      sponsorPackageId: null,
      name: null,
      picName: null,
      picEmail: null,
      picContactNumber: null,
      amountReceived: 0,
      address: null,
      customNote: null,
      picPosition: null,
    }
  }
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.Sponsor.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.Sponsor.UpdateParams)
  }

  modalVisible.value = false
}

const sponsorPackageOptions = computed(() =>
  (sponsorPackageStore.sponsorPackageList.map(item => ({
    label: `${item.name}`,
    value: item.id,
  }))) || [],
)

const title = computed(() => ({
  add: 'CREATE VENDOR',
  edit: 'EDIT VENDOR',
}[props.type || 'add']))

watch(() => props.visible, async (newValue) => {
  eventId.value = eventStore.selectedEvent?.id || 0

  if (newValue)
    updateFormModelByModalType()
})

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0 || Number.isNaN(newValue)) {
    return
  }

  sponsorPackageStore.eventId = eventId.value
  sponsorPackageStore.fetchSponsorPackageList()
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    class="w-500px"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="rounded-theme">
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-ranking />
              <b>{{ title }}</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            The vendor streamlines vendor management, enabling organizers to track, engage, and assign sponsors to the right opportunities for maximum impact and visibility.
          </div>
        </n-flex>
      </template>
      <n-form label-placement="top" :model="formModel" label-align="left">
        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item label="Name" path="name">
            <n-input
              v-model:value="formModel.name"
              show-count
              :maxlength="100"
            />
          </n-form-item-grid-item>
        </n-grid>

        <n-grid cols="2" x-gap="18" responsive="screen">
          <n-form-item-grid-item label="PIC Name" path="picName">
            <n-input
              v-model:value="formModel.picName"
              class="w-full"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="PIC Email" path="picEmail">
            <n-input
              v-model:value="formModel.picEmail"
              class="w-full"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="PIC Contact Number" path="picContactNumber">
            <n-input
              v-model:value="formModel.picContactNumber"
              class="w-full"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="PIC Position" path="picPosition">
            <n-input
              v-model:value="formModel.picPosition"
              class="w-full"
            />
          </n-form-item-grid-item>
        </n-grid>

        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item label="Address" path="address">
            <n-input
              v-model:value="formModel.address"
              :show-button="false"
              class="w-full"
              type="textarea"
              :rows="2"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Custom Note" path="name">
            <n-input
              v-model:value="formModel.customNote"
              :show-button="false"
              class="w-full" show-count
              type="textarea"
              :rows="3"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Sponsor Package" path="sponsorPackageId">
            <n-select
              v-model:value="formModel.sponsorPackageId"
              filterable
              :placeholder="$t('common.select')"
              :options="sponsorPackageOptions"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Amount Received" path="amountreceived">
            <n-input-number
              v-model:value="formModel.amountReceived"
              class="w-full"
            />
          </n-form-item-grid-item>
        </n-grid>
      </n-form>

      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" :loading="loading" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
