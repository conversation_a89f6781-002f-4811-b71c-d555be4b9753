<script setup lang="ts">
import SponsorTypeChart from './components/SponsorTypeChart.vue'
import { useEventStore } from '@/store/event'
import { useSponsorStore } from '@/store/sponsor'
import { useSponsorPackageStore } from '@/store/sponsor-package'

// interface
interface SelectedPackage {
  collectedAmount: number
  collectedPercentage: number
  name: string
  pendingAmount: number
  totalAmount: number
  totalVendor: number
  package?: Entity.SponsorPackage
}

// stores
const eventStore = useEventStore()
const sponsorStore = useSponsorStore()
const sponsorPackageStore = useSponsorPackageStore()

// etc
const eventId = ref<number>(0)
const selectedPackage = ref<SelectedPackage>({
  collectedAmount: 0,
  collectedPercentage: 0,
  name: '',
  pendingAmount: 0,
  totalAmount: 0,
  totalVendor: 0,
  package: {
    speakingSlotDuration: 0,
    amountSponsored: 0,
    customNote: '',
    eventId: 0,
    id: 0,
    name: '',
    sortOrder: 0,
    totalBooths: 0,
    totalTables: 0,
    totalTickets: 0,
  },
})

function handleChangeSelectedPackage(row: SelectedPackage) {
  const sponsorPackage = sponsorPackageStore.sponsorPackageList.find(pkg => pkg.name === row.name)
  selectedPackage.value = { ...row, package: sponsorPackage }
}

onBeforeMount(async () => {
  eventId.value = Number(eventStore.selectedEvent?.id) || 0
})

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0) {
    return
  }
  sponsorStore.eventId = Number(newValue)
  await sponsorStore.fetchVendorDashboard()
  const data: SelectedPackage | undefined = sponsorStore.vendorDashboardData?.sponsorProgresses[0]
  const sponsorPackage = sponsorPackageStore.sponsorPackageList.find(pkg => pkg.name === data?.name)
  if (data) {
    data.package = sponsorPackage
  }
  else {
    console.error('No data found')
  }

  selectedPackage.value = data ?? {
    collectedAmount: 0,
    collectedPercentage: 0,
    name: '',
    pendingAmount: 0,
    totalAmount: 0,
    totalVendor: 0,
    package: {
      speakingSlotDuration: 0,
      amountSponsored: 0,
      customNote: '',
      eventId: 0,
      id: 0,
      name: '',
      sortOrder: 0,
      totalBooths: 0,
      totalTables: 0,
      totalTickets: 0,
    },
  }
})
</script>

<template>
  <div v-if="sponsorStore.vendorDashboardData">
    <div class="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-4 gap-6">
      <n-card :bordered="false" class="rounded-theme shadow">
        <n-space
          align="center"
        >
          <n-icon
            color="#7f9a66"
            size="42"
            class="me-6"
          >
            <icon-park-outline-shop />
          </n-icon>
          <n-statistic label="Vendor">
            <n-number-animation
              :from="0"
              :to="sponsorStore.vendorDashboardData?.totalVendor"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>

      <n-card :bordered="false" class="rounded-theme shadow">
        <n-space
          align="center"
        >
          <n-icon
            color="#7f9a66"
            size="42"
            class="me-6"
          >
            <icon-park-outline-funds />
          </n-icon>
          <n-statistic label="Pending">
            $
            <n-number-animation
              :from="0"
              :to="sponsorStore.vendorDashboardData?.sponsorAmount.pending"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>

      <n-card :bordered="false" class="rounded-theme shadow">
        <n-space
          align="center"
        >
          <n-icon
            color="#7f9a66"
            size="42"
            class="me-6"
          >
            <icon-park-outline-funds />
          </n-icon>
          <n-statistic label="Collected Amount">
            $
            <n-number-animation
              :from="0"
              :to="sponsorStore.vendorDashboardData?.sponsorAmount.collected"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>

      <n-card :bordered="false" class="rounded-theme shadow bg-[#7f9a66]">
        <n-space
          align="center"
        >
          <n-icon
            color="#fff"
            size="42"
            class="me-6"
          >
            <icon-park-outline-funds />
          </n-icon>
          <n-statistic label="Total Amount" class="statistic-label">
            $
            <n-number-animation
              :from="0"
              :to="sponsorStore.vendorDashboardData?.sponsorAmount.total"
              show-separator
            />
          </n-statistic>
        </n-space>
      </n-card>
    </div>

    <div class="flex flex-col 2xl:flex-row gap-7 mt-8">
      <div class="w-full 2xl:w-76%">
        <n-card :bordered="false" class="rounded-theme shadow h-full">
          <div class="flex flex-col xl:flex-row gap-6 h-full">
            <div class="w-full xl:w-35% min-h-400px">
              <n-flex justify="space-between" align="center">
                <span class="text-3xl fw-semibold">{{ selectedPackage.name }}</span>
                <span class="text-xl fw-medium">${{ Number(selectedPackage.totalAmount).toLocaleString() }}</span>
              </n-flex>
              <n-divider style="margin:10px 0;" />
              <n-flex justify="space-between" align="center" class="mt-4">
                <span class="text-muted">Collected Amount</span>
                <span class="fw-medium">${{ Number(selectedPackage.collectedAmount).toLocaleString() }}</span>
              </n-flex>
              <n-flex justify="space-between" align="center" class="mt-4">
                <span class="text-muted">Pending Amount</span>
                <span class="fw-medium">${{ Number(selectedPackage.pendingAmount).toLocaleString() }}</span>
              </n-flex>
              <n-flex justify="space-between" align="center" class="mt-4">
                <span class="text-muted">Sponsor(s)</span>
                <span class="fw-medium">{{ selectedPackage.totalVendor }}</span>
              </n-flex>
              <n-flex justify="space-between" align="center" class="mt-4">
                <span class="text-muted">Speaking Slots</span>
                <span class="fw-medium">{{ selectedPackage.package?.speakingSlotDuration }}</span>
              </n-flex>
              <n-flex justify="space-between" align="center" class="mt-4">
                <span class="text-muted">Ticket(s)</span>
                <span class="fw-medium">{{ selectedPackage.package?.totalTickets }}</span>
              </n-flex>
              <n-flex justify="space-between" align="center" class="mt-4">
                <span class="text-muted">Booth(s)</span>
                <span class="fw-medium">{{ selectedPackage.package?.totalBooths }}</span>
              </n-flex>
              <n-flex justify="space-between" align="center" class="mt-4">
                <span class="text-muted">Table(s)</span>
                <span class="fw-medium">{{ selectedPackage.package?.totalTables }}</span>
              </n-flex>
              <n-flex justify="space-between" align="start" class="mt-4" vertical>
                <span class="block text-muted">Table(s)</span>
                <div class="flex flex-wrap gap-3 mt-2">
                  <div
                    v-for="group in selectedPackage?.package?.sponsors?.slice(0, 4)"
                    :key="group.id"
                    class="rounded-full bg-white shadow px-5 py-2"
                  >
                    <span class="text-[#448469] fw-bold">{{ group.name }}</span>
                  </div>
                </div>
              </n-flex>
            </div>

            <div class="w-full xl:w-65% border-l border-[#efeff5] px-4">
              <div
                v-for="(item, index) in sponsorStore.vendorDashboardData.sponsorProgresses"
                :key="index"
                class="flex gap-x-4 mb-4 cursor-pointer py-2"
                :class="selectedPackage.name === item.name ? ' bg-[#7F9A6655] rounded-xl' : ''"
                @click="handleChangeSelectedPackage(item)"
              >
                <div class="w-45px min-w-45px text-end text-muted">
                  <span>{{ item.collectedPercentage.toFixed(0) }}%</span>
                </div>
                <div class="min-w-70%">
                  <n-progress
                    :border-radius="25"
                    :height="20"
                    color="#7f9a66"
                    type="line"
                    :percentage="item.collectedPercentage"
                    :show-indicator="false"
                    processing
                    rail-color="#F2F2F2"
                  />
                </div>
                <n-ellipsis>
                  <span class="text-muted">
                    {{ item.name }}
                  </span>
                </n-ellipsis>
              </div>
            </div>
          </div>
        </n-card>
      </div>

      <div class="w-full 2xl:w-24%">
        <n-card :bordered="false" class="rounded-theme shadow h-full">
          <SponsorTypeChart :sponsor-packages="sponsorStore.vendorDashboardData.sponsorPackages" />
        </n-card>
      </div>
    </div>
  </div>

  <!-- Skeleton -->
  <div v-else>
    <n-grid
      :x-gap="30"
      :y-gap="30"
    >
      <n-gi :span="6">
        <n-card :bordered="false" class="rounded-theme shadow">
          <n-skeleton :repeat="3" class="mb-2" />
        </n-card>
      </n-gi>
      <n-gi :span="6">
        <n-card :bordered="false" class="rounded-theme shadow">
          <n-skeleton :repeat="3" class="mb-2" />
        </n-card>
      </n-gi>
      <n-gi :span="6">
        <n-card :bordered="false" class="rounded-theme shadow">
          <n-skeleton :repeat="3" class="mb-2" />
        </n-card>
      </n-gi>
      <n-gi :span="6">
        <n-card :bordered="false" class="rounded-theme shadow bg-[#7f9a66]">
          <n-skeleton :repeat="3" class="mb-2" />
        </n-card>
      </n-gi>
    </n-grid>

    <n-grid :x-gap="30" :y-gap="30" class="mt-12" cols="24" item-responsive responsive="screen">
      <n-gi span="24 xl:18" class="min-h-400px ">
        <n-card :bordered="false" class="rounded-theme shadow h-full">
          <div class="gap-6 h-full">
            <n-skeleton :repeat="16" class="mb-2" />
          </div>
        </n-card>
      </n-gi>

      <n-gi span="24 xl:6">
        <n-card :bordered="false" class="rounded-theme shadow h-full">
          <n-skeleton :repeat="16" class="mb-2" />
        </n-card>
      </n-gi>
    </n-grid>
  </div>
</template>

<style scoped>
.statistic-label * {
  color:white !important;
}
</style>
