<script setup lang="ts">
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue'
import { Group, Image, Leafer, PointerEvent, Rect } from 'leafer-ui'
import '@leafer-in/viewport'

const leafer = ref<Leafer | null>(null)
const floorPlanRef = ref<HTMLElement | null>(null)
const placementMode = ref<string | null>(null)
const mouseX = ref<number>(0)
const mouseY = ref<number>(0)

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

let previewShape: Rect | null = null

onMounted(() => {
  nextTick(() => {
    if (floorPlanRef.value) {
      const container = floorPlanRef.value.getBoundingClientRect()
      leafer.value = new Leafer({
        view: floorPlanRef.value,
        width: container.width,
        height: container.height,
        fill: '#f5f5f5',
        type: 'viewport',
      })

      window.addEventListener('resize', handleResize)

      leafer.value?.on('click', (event: MouseEvent) => {
        if (placementMode.value) {
          const view = leafer.value
          if (!view)
            return

          const matrix = view.__
          if (!matrix || matrix.x === undefined || matrix.y === undefined || matrix.scale === undefined)
            return

          let scaleX: number, scaleY: number
          if (typeof matrix.scale === 'number') {
            scaleX = scaleY = matrix.scale
          }
          else {
            scaleX = matrix.scale.x
            scaleY = matrix.scale.y
          }

          const worldX = (event.x - matrix.x) / scaleX
          const worldY = (event.y - matrix.y) / scaleY

          addElementAtPosition(worldX, worldY)
        }

        // Remove preview shape after placement
        if (previewShape) {
          leafer.value?.remove(previewShape)
          previewShape = null
        }
      })

      leafer.value?.on(PointerEvent.MOVE, (event: MouseEvent) => {
        const view = leafer.value
        if (!view)
          return

        const matrix = view.__
        if (!matrix || matrix.x === undefined || matrix.y === undefined || matrix.scale === undefined)
          return

        let scaleX: number, scaleY: number
        if (typeof matrix.scale === 'number') {
          scaleX = scaleY = matrix.scale
        }
        else {
          scaleX = matrix.scale.x
          scaleY = matrix.scale.y
        }

        mouseX.value = (event.x - matrix.x) / scaleX
        mouseY.value = (event.y - matrix.y) / scaleY

        // Create or update preview shape
        if (placementMode.value && !previewShape) {
          previewShape = createPreviewShape(mouseX.value, mouseY.value)
          leafer.value?.add(previewShape)
        }
        else if (previewShape) {
          updatePreviewShapePosition(previewShape, mouseX.value, mouseY.value)
        }
      })
    }
  })
})

function createPreviewShape(x: number, y: number): Rect {
  switch (placementMode.value) {
    case 'table':
      return new Rect({
        x: x - 40, // Adjust width for center positioning
        y: y - 40, // Adjust height for center positioning
        width: 80,
        height: 80,
        fill: '#4CAF50',
        opacity: 0.5, // Make it semi-transparent for preview
        draggable: false, // Don't allow dragging
        cornerRadius: 10,
      })
    case 'booth':
      return new Rect({
        x: x - 60,
        y: y - 40,
        width: 120,
        height: 80,
        fill: '#2196F3',
        opacity: 0.5,
        draggable: false,
        cornerRadius: 20,
      })
    case 'rectangle':
      return new Rect({
        x: x - 50,
        y: y - 30,
        width: 100,
        height: 60,
        fill: '#FF9800',
        opacity: 0.5,
        draggable: false,
      })
    default:
      return new Rect({
        x: x - 50,
        y: y - 30,
        width: 100,
        height: 60,
        fill: '#FF9800',
        opacity: 0.5,
        draggable: false,
      })
  }
}

// Function to update preview shape position
function updatePreviewShapePosition(preview: Rect, x: number, y: number) {
  preview.x = x - preview.width / 2
  preview.y = y - preview.height / 2
}

function isOverlapping(x: number, y: number, width: number, height: number): boolean {
  if (!leafer.value)
    return false

  const children = leafer.value.children
  for (const child of children) {
    if ('x' in child && 'y' in child && 'width' in child && 'height' in child) {
      const childX = child.x
      const childY = child.y
      const childWidth = child.width
      const childHeight = child.height

      const isOverlap
        = x < childX + childWidth
        && x + width > childX
        && y < childY + childHeight
        && y + height > childY

      if (isOverlap)
        return true
    }
  }
  return false
}

function handleResize() {
  if (leafer.value && floorPlanRef.value) {
    const container = floorPlanRef.value.getBoundingClientRect()
    leafer.value.resize({ width: container.width, height: container.height })
  }
}

function togglePlacementMode(type: string) {
  placementMode.value = placementMode.value === type ? null : type
}

function addElementAtPosition(x: number, y: number) {
  if (!leafer.value)
    return

  switch (placementMode.value) {
    case 'table':
      addTable(x, y)
      break
    case 'booth':
      addBooth(x, y)
      break
    case 'rectangle':
      addRectangle(x, y)
      break
  }
}

function addTable(x: number, y: number) {
  const width = 80
  const height = 80
  const adjustedX = x - width / 2
  const adjustedY = y - height / 2

  if (isOverlapping(adjustedX, adjustedY, width, height)) {
    window.$message.warning('Cannot place element here: overlapping another element')
    return
  }

  const table = new Rect({
    x: adjustedX,
    y: adjustedY,
    width,
    height,
    fill: '#4CAF50',
    draggable: true,
    cornerRadius: 10,
  })

  leafer.value?.add(table)
}

function addBooth(x: number, y: number) {
  const width = 120
  const height = 80
  const adjustedX = x - width / 2
  const adjustedY = y - height / 2

  if (isOverlapping(adjustedX, adjustedY, width, height)) {
    window.$message.warning('Cannot place element here: overlapping another element')
    return
  }

  const booth = new Rect({
    x: adjustedX,
    y: adjustedY,
    width,
    height,
    fill: '#2196F3',
    draggable: true,
    cornerRadius: 20,
  })

  leafer.value?.add(booth)
}

function addRectangle(x: number, y: number) {
  const width = 100
  const height = 60
  const adjustedX = x - width / 2
  const adjustedY = y - height / 2

  if (isOverlapping(adjustedX, adjustedY, width, height)) {
    window.$message.warning('Cannot place element here: overlapping another element')
    return
  }

  const rectangle = new Rect({
    x: adjustedX,
    y: adjustedY,
    width,
    height,
    fill: '#FF9800',
    draggable: true,
  })

  leafer.value?.add(rectangle)
}

function zoomIn() {
  if (!leafer.value)
    return
  leafer.value.zoom('in')
}

function zoomOut() {
  if (!leafer.value)
    return
  leafer.value.zoom('out')
}

function fitToView() {
  if (!leafer.value)
    return
  leafer.value.zoom('fit')
}

function clearCanvas() {
  if (leafer.value) {
    leafer.value.removeAll()
  }
}
</script>

<template>
  <div class="floor-plan-container">
    <!-- Toolbar -->
    <div class="toolbar">
      <n-button-group>
        <n-button
          :type="placementMode === 'table' ? 'primary' : 'default'"
          @click="togglePlacementMode('table')"
        >
          Add Table
        </n-button>
        <n-button
          :type="placementMode === 'booth' ? 'primary' : 'default'"
          @click="togglePlacementMode('booth')"
        >
          Add Booth
        </n-button>
        <n-button
          :type="placementMode === 'rectangle' ? 'primary' : 'default'"
          @click="togglePlacementMode('rectangle')"
        >
          Add Rectangle
        </n-button>
        <n-button type="error" @click="clearCanvas">
          Clear All
        </n-button>
      </n-button-group>
    </div>

    <!-- Canvas -->
    <div ref="floorPlanRef" class="floor-plan" />

    <!-- Zoom Controls -->
    <div class="zoom-controls">
      <n-button-group>
        <n-button @click="zoomOut">
          -
        </n-button>
        <n-button @click="fitToView">
          Fit
        </n-button>
        <n-button @click="zoomIn">
          +
        </n-button>
      </n-button-group>
    </div>

    <!-- Mouse Coordinates Display -->
    <div
      style="position: absolute; top: 10px; right: 10px; background: rgba(0, 0, 0, 0.6); color: white; padding: 6px 10px; border-radius: 4px; font-size: 14px;"
    >
      X: {{ mouseX.toFixed(2) }}, Y: {{ mouseY.toFixed(2) }}
    </div>
  </div>
</template>

<style scoped>
.floor-plan-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 10px;
  background: #fff;
  border-bottom: 1px solid #eee;
  display: flex;
  gap: 10px;
}

.floor-plan {
  flex: 1;
  background: #f5f5f5;
  position: relative;
  cursor: crosshair;
}

.zoom-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 100;
}
</style>
