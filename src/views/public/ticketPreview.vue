<script setup lang="tsx">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { useTicketStore } from '@/store/ticket'

const route = useRoute()
const ticketStore = useTicketStore()
const ticketPreviewData = ref('')

onMounted(async () => {
  let ticketNumber = ''
  if (route.query.ticketNumber) {
    ticketNumber = route.query.ticketNumber.toString()
  }
  const data = await ticketStore.publicTicketPreview(ticketNumber)
  ticketPreviewData.value = data
})
</script>

<template>
  <div id="ticket-content" v-html="ticketPreviewData" />
</template>
