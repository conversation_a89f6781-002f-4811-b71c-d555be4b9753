<script setup lang="tsx">
import type { UploadFileInfo } from 'naive-ui'
import { NButton, NPopconfirm, NSpace } from 'naive-ui'
import { ref } from 'vue'
import { Icon } from '@iconify/vue'
import TableModal from './components/TableModal.vue'
import { useBoolean } from '@/hooks'
import { formatDate } from '@/utils'
import { useEventDetailStore } from '@/store/event-detail'
import { useEventStore } from '@/store/event'
import SourClick from '@/assets/sour-click.svg'
import Lime from '@/assets/lime.svg'
import DefaultSubEventImage from '@/assets/default-sub-event.svg'

// store
const route = useRoute()
const router = useRouter()
const eventStore = useEventStore()
const eventDetailStore = useEventDetailStore()

// loading states
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// visibility states
const { bool: formModalVisible, setTrue: openFormModal } = useBoolean(false)

// etc
const fileList = ref<UploadFileInfo[]>([])
const modalType = ref<ModalType>('add')
const buttons = [
  {
    id: 1,
    action: 'hallLayout',
    icon: 'tabler:layout-2',
    label: 'Hall Layout',
    name: 'Event Hall',
  },
  {
    id: 2,
    action: 'checkIn',
    icon: 'icon-park-outline:check-in',
    label: 'Check In',
    name: 'Event Check In',
  },
  {
    id: 3,
    action: 'luckyDraw',
    icon: 'icon-park-outline:gift',
    label: 'Lucky Draw',
    name: 'Event Lucky Draw',
  },
]

// functions
async function fetchEventDetailList() {
  startLoading()
  await eventDetailStore.fetchEventDetailList()
  endLoading()
}

async function handleCreate(data: Entity.EventDetail.CreateParams) {
  const item = await eventDetailStore.createEventDetail(data)
  if (fileList.value.length > 0 && item.id) {
    await eventDetailStore.uploadImage(item.id, fileList.value)
  }
  fileList.value = []
  fetchEventDetailList()
}

async function handleUpdate(data: Entity.EventDetail.UpdateParams) {
  await eventDetailStore.updateEventDetail(data)
  if (fileList.value.length > 0 && data.id) {
    await eventDetailStore.uploadImage(data.id, fileList.value)
  }
  fileList.value = []
  fetchEventDetailList()
}

async function handleDelete(id: number) {
  await eventDetailStore.deleteEventDetail(id)
  fetchEventDetailList()
}

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleEdit(row: Entity.EventDetail) {
  eventDetailStore.editData = row
  fileList.value = row.pictureUrl
    ? [{
        id: row.id.toString(),
        name: row.name,
        status: 'finished',
        url: row.pictureUrl,
      }]
    : []
  setModalType('edit')
  openFormModal()
}

function handleAddTable() {
  eventDetailStore.editData = null
  fileList.value = []
  setModalType('add')
  openFormModal()
}

function handleSelect(pathName: string, params?: Record<string, any>) {
  const routeData = router.resolve({ name: pathName, query: params })
  window.open(routeData.href, '_blank')
}

onMounted(async () => {
  eventDetailStore.eventId = eventStore.selectedEvent?.id || 0
  await fetchEventDetailList()

  if (route.query.openFormModal === 'true') {
    formModalVisible.value = true
    window.history.replaceState(null, '', '/#/event-details')
  }
})
</script>

<template>
  <NSpace size="large" vertical class="flex-1 py-2" style="gap:30px !important;">
    <n-grid v-if="loading" cols="1" responsive="screen" x-gap="16" y-gap="16">
      <n-grid-item v-for="i in [0, 1]" :key="i">
        <n-card class="rounded-theme shadow h-full min-h-200px cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition">
          <template #header>
            <n-skeleton text class="w-100%" />
          </template>
          <div class="flex gap-4">
            <n-skeleton text class="w-100px h-100px" />
            <div class="flex-1">
              <n-skeleton text :repeat="4" class="w-100%" />
            </div>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid v-else cols="1" responsive="screen" x-gap="16" y-gap="16" style="gap:30px !important;">
      <template v-if="eventDetailStore.eventDetailList.length > 0">
        <n-grid-item v-for="item in eventDetailStore.eventDetailList" :key="item.id">
          <n-card class="shadow rounded-theme w-full py-1 relative hover:filter-drop-shadow hover:brightness-95 transition">
            <n-image :src="Lime" class="absolute top-[-12px] left-[-12px] w-34px object-cover rotate-340" preview-disabled />

            <div class="flex flex-col 2xl:flex-row">
              <div class="w-170px h-170px p-4">
                <n-image
                  :src="item.pictureUrl ?? DefaultSubEventImage"
                  class="w-full h-full rounded-theme"
                  object-fit="cover"
                />
              </div>

              <div class="flex flex-col flex-1">
                <div class="flex">
                  <n-ellipsis :line-clamp="1" class="text-[1.5rem] font-bold">
                    {{ item.name }}
                  </n-ellipsis>
                </div>

                <div class="flex my-auto gap-x-12">
                  <div class="flex flex-col" style="gap:0;">
                    <span class="text-sm text-muted">Check-In Start At</span>
                    <span class="text-base fw-semibold">{{ formatDate(item.checkInStartAt) }}</span>
                  </div>

                  <n-flex vertical style="gap:0;">
                    <span class="text-sm text-muted">Check-In End At</span>
                    <span class="text-base fw-semibold">{{ formatDate(item.checkInEndAt) }}</span>
                  </n-flex>
                </div>

                <div class="flex min-w-[120px] 2xl:my-0 my-4 gap-4">
                  <NButton icon-placement="left" type="default" @click="handleEdit(item)">
                    <template #icon>
                      <icon-park-outline-edit-two />
                    </template>
                  </NButton>

                  <NPopconfirm @positive-click="handleDelete(item.id)">
                    <template #trigger>
                      <NButton type="error" ghost>
                        <template #icon>
                          <icon-park-outline-delete class="text-sm" />
                        </template>
                      </NButton>
                    </template>
                    Confirm Delete?
                  </NPopconfirm>
                </div>
              </div>

              <div class="flex flex-col gap-3 w-full 2xl:w-480px">
                <div v-for="button in buttons" :key="button.id" :bordered="false" class="py-1 bg-theme shadow flex cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition rounded-xl" @click="handleSelect(button.action, { eventDetailId: item.id, eventId: eventStore.selectedEvent?.id, eventDetailName: button.name })">
                  <div class="flex">
                    <span class="text-white px-4 py-2" :bordered="false">
                      <Icon :icon="button.icon" font-size="6" />
                    </span>
                    <span class="text-white text-lg my-auto">{{ button.label }}</span>
                  </div>
                </div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </template>

      <n-grid-item>
        <n-card class="rounded-theme shadow h-full min-h-400px cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition" content-class="flex items-center justify-center" @click="handleAddTable">
          <div class="flex flex-col items-center opacity-[0.7]">
            <img :src="SourClick" alt="" class="max-w-100% sm:h-[80px]">
            <h2 class="text-gray-800 font-bold text-2xl sm:text-3xl md:text-4xl mt-8 mb-2 text-success text-center">
              BUILD AN EXPERIENCE WITH US
            </h2>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <TableModal
      v-model:visible="formModalVisible"
      v-model:file-list="fileList"
      :type="modalType"
      :modal-data="eventDetailStore.editData"
      :on-create="handleCreate"
      :on-update="handleUpdate"
      :on-delete="handleDelete"
    />
  </NSpace>
</template>
