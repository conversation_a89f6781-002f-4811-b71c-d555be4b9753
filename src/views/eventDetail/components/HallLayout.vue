<script setup>
import { ref } from 'vue'
import FullTableOverviewModal from './FullTableOverviewModal.vue'
import TableImage from '@/assets/table.png'
import BlockImage from '@/assets/block.png'
import { useTicketStore } from '@/store/ticket'
import { useBoolean } from '@/hooks'
import { useEventDetailStore } from '@/store/event-detail'

const eventDetailStore = useEventDetailStore()

// Reactive State
const layout = ref([])
const selectedItem = ref({ name: null, x: 0, y: 0, w: 2, h: 2, id: -1, guests: [] })
const drawerActive = ref(false)
const isDragging = ref(false)
const dragStartPosition = ref({ x: 0, y: 0 })
const tempTicketList = ref([])
const { bool: modalVisible, setTrue: openModal } = useBoolean(false)
const tickcetList = ref([])
const { bool: formDirty, setTrue: setFormDirtyTrue, setFalse: setFormDirtyFalse } = useBoolean(false)

const route = useRoute()
const ticketStore = useTicketStore()

async function getLayout() {
  const data = (await eventDetailStore.getEventDetail(route.query.eventDetailId)).hallLayout

  if (data && data.length > 0) {
    let blockCount = 0
    data.forEach((item) => {
      if (item.type === 'block') {
        item.id = `block${blockCount++}`
      }
    })

    layout.value = data
  }
}

function generateRandomString(length = 16) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const randomValues = new Uint8Array(length)
  window.crypto.getRandomValues(randomValues)
  return Array.from(randomValues).map(value => charset[value % charset.length]).join('')
}

function saveLayout() {
  const params = {
    eventDetailId: Number(route.query.eventDetailId),
    hallLayout: layout.value,
  }
  eventDetailStore.saveLayout(params)
  setFormDirtyFalse()
  window.$message.success('Layout saved successfully!')
}

function resetLayout() {
  layout.value = []
  setFormDirtyTrue(true)
  window.$message.success('Layout reset successfully!')
}

function addTable() {
  layout.value.push({
    id: generateRandomString(16),
    type: 'table',
    name: 'Table',
    x: 0,
    y: 0,
    w: 2,
    h: 2,
    resizable: false,
    capacity: 6,
    occupied: 0,
    guests: [],
  })
  setFormDirtyTrue()
}

function addBlock() {
  layout.value.push({
    id: generateRandomString(16),
    type: 'block',
    name: 'Block',
    x: 0,
    y: 0,
    w: 4,
    h: 4,
    resizable: true,
    capacity: 0,
  })
  setFormDirtyTrue()
}

function calculateFontSize(item) {
  const width = item.w * 12
  const minFontSize = 8
  const maxFontSize = 20

  const fontSize = Math.min(maxFontSize, Math.max(minFontSize, width / 3))

  return `${fontSize}px`
}

function deleteItem(id, event) {
  event.stopPropagation()
  layout.value = layout.value.filter(item => item.id !== id)
}

function openUpdateDrawer(item) {
  if (!isDragging.value) {
    selectedItem.value = item
    drawerActive.value = true
  }
  setFormDirtyTrue()
}

function handleMouseDown(event) {
  dragStartPosition.value = { x: event.clientX, y: event.clientY }
  isDragging.value = false
  setFormDirtyTrue()
}

function handleMouseMove(event) {
  const distance = Math.sqrt(
    (event.clientX - dragStartPosition.value.x) ** 2
    + (event.clientY - dragStartPosition.value.y) ** 2,
  )
  if (distance > 1) {
    isDragging.value = true
  }
  setFormDirtyTrue()
}

async function handleMouseUp(item) {
  if (!isDragging.value) {
    ticketStore.ticketIds = item.guests
    const data = await ticketStore.fetchTicketListByTicketIds()

    // Ensure data is an array before assigning it
    tempTicketList.value = Array.isArray(data) ? data : []

    openUpdateDrawer(item)
  }
  setFormDirtyTrue()
}

const selectedGuests = computed(() => {
  if (selectedItem.value.type === 'table') {
    return tickcetList.value.filter(ticket => selectedItem.value.guests.includes(ticket.id))
  }
  return []
})

const occupiedSeats = computed(() => {
  if (selectedItem.value.type === 'table') {
    return selectedItem.value.guests.length
  }
  return 0
})

const isCapacityExceeded = computed(() => {
  if (selectedItem.value.type === 'table') {
    return selectedItem.value.capacity !== false && occupiedSeats.value >= selectedItem.value.capacity
  }
  return false
})

const statusType = computed(() => {
  if (selectedItem.value.type === 'table') {
    if (isCapacityExceeded.value)
      return 'error'
    if (selectedItem.value.guests.length === 0)
      return 'success'
    return 'warning'
  }
  return 'default'
})

async function fetchTicketList() {
  tickcetList.value = await ticketStore.fetchTicketListWithoutPagination()
}

const statusText = computed(() => {
  if (selectedItem.value.type === 'table') {
    if (isCapacityExceeded.value)
      return 'Full'
    if (selectedItem.value.guests.length === 0)
      return 'Available'
    return 'Occupied'
  }
  return 'Not Applicable'
})

function handleSelectChange(newGuests) {
  if (isCapacityExceeded.value) {
    selectedItem.value.guests = newGuests.filter(guest =>
      selectedItem.value.guests.includes(guest) || !isCapacityExceeded.value,
    )
  }
  else {
    selectedItem.value.guests = newGuests
  }
  setFormDirtyTrue()
}

const filteredTickets = computed(() => {
  const selectedTickets = layout.value.flatMap(item => item.guests)
  const allTickets = [...tickcetList.value, ...tempTicketList.value]
  const uniqueTickets = Array.from(new Map(allTickets.map(ticket => [ticket.id, ticket])).values())

  return uniqueTickets.map(ticket => ({
    label: ticket.holderName,
    value: ticket.id,
    disabled: (selectedTickets.includes(ticket.id) && !selectedItem.value.guests.includes(ticket.id))
    || (selectedItem.value.guests.length >= selectedItem.value.capacity && !selectedItem.value.guests.includes(ticket.id)),
  }))
})

function handleBeforeUnload(event) {
  if (formDirty.value === true) {
    const message = 'You have unsaved changes. Are you sure you want to leave?'
    event.returnValue = message
    return message
  }
}

function truncateName(name) {
  return name && name.length > 5 ? `${name.slice(0, 5)}...` : name
}

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

watch(() => selectedItem.value.guests, (newGuests) => {
  if (selectedItem.value.type === 'table') {
    const table = layout.value.find(item => item.id === selectedItem.value.id)
    if (table) {
      table.occupied = newGuests.length
    }
  }
})

watch(() => selectedItem.value.guests, (newGuests) => {
  const target = layout.value.find(item => item.id === selectedItem.value.id)
  if (target && target.type === 'table') {
    target.occupied = newGuests.length
  }
})

onMounted(async () => {
  window.addEventListener('beforeunload', handleBeforeUnload)
  ticketStore.eventId = Number(route.query.eventId)
  await getLayout()
  await fetchTicketList()
})
</script>

<template>
  <n-flex class="flex-1" vertical>
    <!-- Drawer -->
    <n-drawer v-model:show="drawerActive" placement="right" min-w-350px>
      <n-drawer-content title="Table Details">
        <n-form>
          <n-form-item label="Status">
            <n-button :type="statusType" class="status-button">
              {{ statusText }}
            </n-button>
          </n-form-item>
          <n-form-item label="Name">
            <n-input id="update-name" v-model:value="selectedItem.name" :maxlength="selectedItem.type === 'table' ? 15 : 10" show-count />
          </n-form-item>
          <n-form-item v-if="selectedItem.type === 'table' && selectedItem.capacity !== false" label="Capacity">
            <n-input-number id="update-capacity" v-model:value="selectedItem.capacity" class="flex-1" min="0" />
          </n-form-item>
          <n-form-item v-if="selectedItem.type === 'table'" label="Guests">
            <n-select
              v-model:value="selectedItem.guests"
              multiple
              filterable
              show-count
              :options="filteredTickets"
              @update="handleSelectChange"
            />
          </n-form-item>
          <n-card v-if="selectedGuests.length > 0">
            <n-list>
              <n-list-item v-for="guest in selectedGuests" :key="guest.id">
                <div><strong>Name:</strong> {{ guest.holderName }}</div>
                <div><strong>Email:</strong> {{ guest.email }}</div>
                <div><strong>Ticket Number:</strong> {{ guest.ticketNumber }}</div>
                <div v-for="(note, index) in guest.customNote" :key="index">
                  <strong>{{ note.key }}</strong>: {{ note.value }}
                </div>
              </n-list-item>
            </n-list>
          </n-card>
        </n-form>
      </n-drawer-content>
    </n-drawer>

    <!-- Panel -->
    <n-card>
      <n-flex>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button class="px-1 w-12 h-12" @click="addTable">
              <img :src="`${TableImage}`" alt="" class="object-fill w-full">
            </n-button>
          </template>
          Add Table
        </n-tooltip>

        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button class="px-2 w-12 h-12" @click="addBlock">
              <img :src="`${BlockImage}`" alt="" class="object-fill w-full">
            </n-button>
          </template>
          Add Block
        </n-tooltip>

        <div class="ml-auto">
          <n-button type="default" @click="openModal">
            View Tables
          </n-button>
        </div>

        <div>
          <n-button type="default" @click="saveLayout">
            Save
          </n-button>
        </div>

        <n-button type="default" @click="resetLayout">
          Reset
        </n-button>
      </n-flex>
    </n-card>

    <!-- Grid Layout -->
    <n-card class="h-full">
      <grid-layout
        v-model:layout="layout"
        :col-num="32"
        :row-height="40"
        :horizontal-shift="true"
        :vertical-compact="false"
        :prevent-collision="true"
      >
        <template #default="{ gridItemProps }">
          <grid-item
            v-for="item in layout"
            v-bind="gridItemProps"
            :id="item.id"
            :key="item.id"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            class="grid-item" :class="{
              occupied: item.type === 'table' && item.occupied > 0 && item.occupied < item.capacity,
              full: item.type === 'table' && item.capacity !== false && item.occupied >= item.capacity,
            }"
            :style="{
              fontSize: calculateFontSize(item),
              backgroundImage: item.type === 'table' ? `url(${TableImage})` : ``,
            }"
            :is-resizable="item.resizable"
            @mousedown="handleMouseDown"
            @mousemove="handleMouseMove"
            @mouseup="() => handleMouseUp(item)"
          >
            <button class="delete-btn" @mousedown.stop @click="(event) => deleteItem(item.id, event)">
              ×
            </button>
            {{ truncateName(item.name) }}
            <template v-if="item.type === 'table'">
              <br>
              {{ item.capacity !== false ? `${item.occupied} / ${item.capacity}` : '' }}
            </template>
          </grid-item>
        </template>
      </grid-layout>
    </n-card>

    <FullTableOverviewModal
      v-model:visible="modalVisible"
      :data="tickcetList"
      :layout="layout"
    />
  </n-flex>
</template>

<style scoped>
.grid-item {
  border: 1px solid #ddd;
  color: #000;
  background-color: #f3f3f3;
  text-align: center;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: contain;
  background-position: center;
  background-repeat:no-repeat;
  transition: box-shadow 0.3s ease;
}

.grid-item:hover {
  background-color: lightgray;
}

.grid-item.occupied {
  border: 2px solid #f0a020;
}

.grid-item.full {
  border: 2px solid #d03050;
}

.delete-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  background: rgba(0, 0, 0);
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  cursor: pointer;
}

.delete-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}
</style>
