<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui'
import { NButton, NDropdown, NIcon, NPopconfirm, NSpace } from 'naive-ui'
import { ref } from 'vue'
import TableModal from './TableModal.vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { formatDate, renderIcon } from '@/utils'
import { useEventDetailStore } from '@/store/event-detail'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: formModalVisible, setTrue: openFormModal } = useBoolean(false)

const eventDetailStore = useEventDetailStore()

const modalType = ref<ModalType>('add')

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleEdit(row: Entity.EventDetail) {
  eventDetailStore.editData = row
  setModalType('edit')
  openFormModal()
}

function handleAddTable() {
  eventDetailStore.editData = null
  setModalType('add')
  openFormModal()
}

async function fetchEventDetailList() {
  startLoading()
  window.$loadingBar.start()
  await eventDetailStore.fetchEventDetailList()
  window.$loadingBar.finish()
  endLoading()
}

async function handleCreate(data: Entity.EventDetail.CreateParams) {
  await eventDetailStore.createEventDetail(data)
  fetchEventDetailList()
}

async function handleUpdate(data: Entity.EventDetail.UpdateParams) {
  await eventDetailStore.updateEventDetail(data)
  fetchEventDetailList()
}

async function handleDelete(id: number) {
  await eventDetailStore.deleteEventDetail(id)
  fetchEventDetailList()
}

const router = useRouter()
const route = useRoute()
function handleSelect(path: string, eventDetailId: number, eventDetailName: any = null) {
  if (path === '/check-ins' || path === '/hall-layout') {
    const url = `/#${path}?eventDetailId=${eventDetailId}&eventId=${route.query.eventId}&eventDetailName=${eventDetailName}`
    window.open(url, '_blank')
  }
  else {
    router.push({
      path,
      query: { eventDetailId, eventId: route.query.eventId },
    })
  }
}

const columns: DataTableColumns<Entity.EventDetail> = [
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row: Entity.EventDetail, index: number) {
      return index + 1 + ((eventDetailStore.currentPage - 1) * eventDetailStore.currentSize)
    },
  },
  { title: $t('event.name'), align: 'center', key: 'name' },
  {
    title: $t('event.checkInStartAt'),
    align: 'center',
    key: 'startAt',
    render(row: Entity.EventDetail) {
      return formatDate(row.checkInStartAt)
    },
  },
  {
    title: $t('event.checkInEndAt'),
    align: 'center',
    key: 'endAt',
    render(row: Entity.EventDetail) {
      return formatDate(row.checkInEndAt)
    },
  },
  {
    title: $t('common.action'),
    align: 'center',
    key: 'actions',
    render(row: Entity.EventDetail) {
      return (
        <NSpace justify="center">
          <NButton
            size="small"
            onClick={() => handleEdit(row)}
          >
            <icon-park-outline-edit-two />
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton size="small" type="error" ghost>
                  <icon-park-outline-delete />
                </NButton>
              ),
            }}
          </NPopconfirm>
          <NDropdown
            trigger="click"
            options={[
              {
                label: 'Hall Layout',
                key: '/hall-layout',
                icon: renderIcon('tabler:layout-2'),
              },
              {
                label: 'Check In',
                key: '/check-ins',
                icon: renderIcon('icon-park-outline:check-in'),
              },
              {
                label: 'Lucky Draw',
                key: '/lucky-draws',
                icon: renderIcon('icon-park-outline:gift'),
              },
            ]}
            onSelect={option => handleSelect(option, row.id, row.name)}
          >
            <NButton size="small">
              <NIcon><icon-park-outline-more-one /></NIcon>
            </NButton>
          </NDropdown>
        </NSpace>
      )
    },
  },
]

onMounted(async () => {
  await fetchEventDetailList()
})
</script>

<template>
  <NSpace size="large" vertical class="flex-1">
    <n-grid v-if="loading" cols="1 s:2 l:3" responsive="screen" x-gap="16" y-gap="16">
      <n-grid-item v-for="i in [0, 1, 2]" :key="i">
        <n-card class="rounded-lg shadow-md h-full min-h-200px cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition">
          <template #header>
            <n-skeleton text class="w-100%" />
          </template>
          <n-flex horizontal>
            <n-skeleton text class="w-100px h-100px" />
            <n-flex class="flex-1">
              <n-skeleton text :repeat="4" class="w-100%" />
            </n-flex>
          </n-flex>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-grid v-else cols="1 s:2 l:3" responsive="screen" x-gap="16" y-gap="16">
      <template v-if="eventDetailStore.eventDetailList.length > 0">
        <n-grid-item v-for="item in eventDetailStore.eventDetailList" :key="item.id">
          <n-badge dot type="info" processing class="w-full" :offset="[-3, 3]">
            <n-card class="shadow-md rounded-lg w-full min-h-150px cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition" @click="handleEdit(item)">
              <template #header>
                <n-ellipsis :line-clamp="1" class="text-lg font-bold">
                  {{ item.name }}asd
                </n-ellipsis>
              </template>

              <n-flex>
                <n-image
                  src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  class="w-100px h-100px"
                />
                <n-flex vertical>
                  <div>
                    <p><b>Check In Start:</b></p>
                    <p class="text-gray-700 text-sm">
                      {{ formatDate(item.checkInStartAt) }}
                    </p>
                  </div>
                  <div>
                    <p><b>Check In End:</b></p>
                    <p class="text-gray-700 text-sm">
                      {{ formatDate(item.checkInEndAt) }}
                    </p>
                  </div>
                </n-flex>
              </n-flex>
            </n-card>
          </n-badge>
        </n-grid-item>
      </template>

      <n-grid-item>
        <n-card class="rounded-lg shadow-md h-full min-h-200px cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition" content-class="flex items-center justify-center" @click="handleAddTable">
          <div class="text-center">
            <NButton class="rounded-full w-12 h-12 text-2xl font-bold mb-4" type="default">
              +
            </NButton>
            <h2 class="text-gray-800 font-bold text-xl mb-2">
              Add New
            </h2>
            <p class="text-gray-500 text-sm">
              Click to create a new event detail
            </p>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>
  </NSpace>

  <TableModal
    v-model:visible="formModalVisible"
    :type="modalType"
    :modal-data="eventDetailStore.editData"
    :on-create="handleCreate"
    :on-update="handleUpdate"
    :on-delete="handleDelete"
  />
</template>
