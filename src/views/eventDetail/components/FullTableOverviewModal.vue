<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue'

// Define specific interfaces for better type safety
interface Guest {
  id: number
  holderName: string
  email: string
  ticketNumber: string
  customNote: Record<string, any>
}

interface LayoutItem {
  name: string
  capacity: number
  guests?: string[]
}

interface Props {
  visible: boolean
  type?: ModalType
  layout: LayoutItem[]
  data: Guest[]
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})

const emit = defineEmits<Emits>()

const modalVisible = computed({
  get: () => props.visible,
  set: (visible: boolean) => emit('update:visible', visible),
})

function findByTicketNumber(id: number): Guest | undefined {
  return props.data.find(item => item.id === id)
}

function removeFalseCapacity(layout: any) {
  return layout.filter((item: any) => item.type !== 'block')
}
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    preset="card"
    title="Full Table Overview"
    class="w-80vw"
    :segmented="{ content: true, action: true }"
  >
    <n-grid cols="4" :x-gap="12" :y-gap="8">
      <n-gi v-for="(item, index) in removeFalseCapacity(props.layout)" :key="index">
        <n-card
          class="h-full" :title="item.name" :segmented="{
            content: true,
            footer: 'soft',
          }"
        >
          <div>
            <div class="mb-4">
              <strong>Guests</strong>:
            </div>
            <n-collapse v-if="item.guests?.length">
              <n-collapse-item v-for="(guestTicketNumber, index2) in item.guests" :key="index2" :title="findByTicketNumber(guestTicketNumber)?.holderName">
                <template v-if="findByTicketNumber(guestTicketNumber)">
                  <div><strong>Email:</strong> {{ findByTicketNumber(guestTicketNumber)?.email }}</div>
                  <div><strong>Ticket Number:</strong> {{ findByTicketNumber(guestTicketNumber)?.ticketNumber }}</div>
                  <div v-for="(note, index3) in findByTicketNumber(guestTicketNumber)?.customNote || {}" :key="index3">
                    <strong>{{ note.key }}</strong>: {{ note.value }}
                  </div>
                </template>
                <template v-else>
                  <div><strong>Guest {{ index2 }}</strong>: Data not found</div>
                </template>
              </n-collapse-item>
            </n-collapse>
            <p v-else>
              No guest in this table
            </p>
          </div>
        </n-card>
      </n-gi>
    </n-grid>
  </n-modal>
</template>

<style scoped></style>
