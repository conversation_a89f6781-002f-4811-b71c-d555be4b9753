<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import moment from 'moment'
import type { UploadFileInfo } from 'naive-ui'
import { $t } from '@/utils/i18n'
import { toUnix } from '@/utils'
import { useEventDetailStore } from '@/store/event-detail'

// Interface declaration
interface Props {
  visible: boolean
  type?: ModalType
  modalData?: Entity.EventDetail | null
  fileList: UploadFileInfo[]
  onCreate?: (data: Entity.EventDetail.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.EventDetail.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'update:file-list', fileList: UploadFileInfo[]): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})

const emit = defineEmits<Emits>()

const eventDetailStore = useEventDetailStore()
const formModel = ref<Nullable<Entity.EventDetail.CreateParams>>(eventDetailStore.defaultFormModel)
const dateRange = ref<[number, number] | null >(null)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.modify'),
}[props.type || 'add']))

const route = useRoute()

function updateFormModelByModalType() {
  formModel.value.eventId = Number(route.query.eventId)
  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData }
    dateRange.value = [toUnix(props.modalData.checkInStartAt), toUnix(props.modalData.checkInEndAt)]
  }
  else {
    formModel.value = {
      eventId: eventDetailStore.eventId,
      hallLayout: null,
      checkInStartAt: null,
      checkInEndAt: null,
      name: null,
    }
    const now = new Date()

    const startOfNowPlusOneHour = new Date(now.getTime() + 1 * 60 * 60 * 1000)
    const endOfToday = new Date(now.setHours(23, 59, 59, 999))

    dateRange.value = [
      toUnix(moment(startOfNowPlusOneHour).format('YYYY-MM-DD HH:mm:ss')),
      toUnix(moment(endOfToday).format('YYYY-MM-DD HH:mm:ss')),
    ]

    formModel.value.checkInStartAt = moment(startOfNowPlusOneHour).format('YYYY-MM-DD HH:mm:ss')
    formModel.value.checkInEndAt = moment(endOfToday).format('YYYY-MM-DD HH:mm:ss')
  }
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.EventDetail.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.EventDetail.UpdateParams)
  }

  modalVisible.value = false
}

function onDateRangeChange(value: any) {
  if (value && value.length === 2) {
    formModel.value.checkInStartAt = moment(value[0]).format('YYYY-MM-DD HH:mm:ss')
    formModel.value.checkInEndAt = moment(value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  else {
    formModel.value.checkInStartAt = null
    formModel.value.checkInEndAt = null
  }
}

function disablePreviousDate(timestamp: number): boolean {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const todayTimestamp = today.getTime()
  return timestamp < todayTimestamp
}

watch(() => props.visible, (newValue) => {
  if (newValue) {
    updateFormModelByModalType()
  }
})

function updateFileList(newFileList: UploadFileInfo[]) {
  emit('update:file-list', newFileList)
}
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    class="w-500px"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="rounded-theme">
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-ticket />
              <b class="uppercase">{{ title }} Sub Event</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            Set up your sub event's name, check in date time to provide participants with the essential information.
          </div>
        </n-flex>
      </template>
      <n-form label-placement="top" :model="formModel" label-align="left">
        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item :label="$t('eventDetail.name')" path="name">
            <n-input
              v-model:value="formModel.name" show-count
              :maxlength="50"
            />
          </n-form-item-grid-item>
          <n-form-item-grid-item :label="$t('common.dateTimeRange')" path="dateRange" :span="2">
            <n-date-picker
              v-model:value="dateRange"
              type="datetimerange"
              clearable
              style="width: 100%"
              :is-date-disabled="disablePreviousDate"
              @update:value="onDateRangeChange"
            />
          </n-form-item-grid-item>
          <!-- <n-form-item-grid-item label="Image" :span="2">
            <div class="flex flex-col">
              <n-upload
                accept="image/jpeg, image/png, image/webp, image/svg+xml"
                :file-list="fileList"
                list-type="image-card"
                :max="1"
                @update:file-list="updateFileList"
              />
              <span class="text-[#999] mt-2 text-xs">
                Maximum file size: 5MB.
                <br>
                Supported formats: JPG, PNG, WebP, SVG.
              </span>
            </div>
          </n-form-item-grid-item> -->
        </n-grid>
      </n-form>
      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
