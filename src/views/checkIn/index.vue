<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui'
import { NButton, NPopconfirm, NSpace } from 'naive-ui'
import Pusher from 'pusher-js'
import { QrcodeStream } from 'vue-qrcode-reader'
import { Icon } from '@iconify/vue'
import TableModal from '../ticket/TableModal.vue'
import ManualCheckInDrawer from '../checkIn/ManualCheckInDrawer.vue'
import { $t } from '@/utils/i18n'
import { formatDate } from '@/utils'
import { useTicketTypeStore } from '@/store/ticket-type'
import { useBoolean } from '@/hooks'
import { useCheckInStore } from '@/store/check-in'
import { useTicketStore } from '@/store/ticket'
import { useDashboardStore } from '@/store/dashboard'
import { useResultWindowStore } from '@/store/result-window'

// store
const route = useRoute()
const ticketStore = useTicketStore()
const checkInStore = useCheckInStore()
const dashboardStore = useDashboardStore()
const ticketTypeStore = useTicketTypeStore()
const resultWindowStore = useResultWindowStore()

// visibility states
const { bool: formModalVisible, setTrue: openFormModal } = useBoolean(false)
const { bool: scannerVisible, setTrue: openScanner, setFalse: closeScanner } = useBoolean(false)
const { bool: manualCheckInDrawerVisible, setTrue: openManualCheckInDrawer } = useBoolean(false)

// loading states
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: exportLoading, setTrue: startExportLoading, setFalse: endExportLoading } = useBoolean(false)

// etc
// const ticketInput = ref()
// const submitButton = ref<ComponentPublicInstance | null>(null)

const modalType = ref<ModalType>('add')
const eventDetailName = ref<string>('')
const showCustomNote = ref<boolean>(false)
const selectedEventDetailId = ref<number>(0)
const lastCheckInTicket = ref<Entity.Ticket>()
const scannerOverlay = ref<HTMLElement | null>(null)
const checkInSummary = ref<Entity.CheckIn.Summary>()
const checkedRowKeysRef = ref<Array<string | number>>([])

async function createCheckIn() {
  if (!checkInStore.formModel.ticketNumber || checkInStore.formModel.ticketNumber === '') {
    window.$message.error('Ticket number is required')
    return
  }

  const params: Entity.CheckIn.CreateParams = {
    eventDetailId: checkInStore.eventDetailId,
    ticketNumber: checkInStore.formModel.ticketNumber,
  }

  const data = await checkInStore.createCheckIn(params)
  await fetchCheckInSummary()
  await fetchCheckInList(true)

  if (data) {
    checkInStore.formModel.ticketNumber = ''
    lastCheckInTicket.value = data

    resultWindowStore.postToResultPage({
      ticket: lastCheckInTicket.value,
      showCustomNote: showCustomNote.value,
    })
  }
}

async function removeCheckIn(id: number) {
  const success = await checkInStore.deleteCheckIn(id)
  if (success) {
    await fetchCheckInList()
    await fetchCheckInSummary()
    await fetchCheckInList(true)
    window.$message.success('Undo check in successfully!')
  }
}

async function viewTicket(ticketNumber: any = null) {
  const params: Entity.CheckIn.CreateParams = {
    eventDetailId: checkInStore.eventDetailId,
    ticketNumber,
  }
  const data = await checkInStore.createCheckIn(params)

  if (data) {
    checkInStore.formModel.ticketNumber = ''
    lastCheckInTicket.value = data

    window.$message.success('Success')
    resultWindowStore.postToResultPage({
      ticket: lastCheckInTicket.value,
      showCustomNote: showCustomNote.value,
    })
  }
}

async function handleCreate(data: Entity.Ticket.CreateGuestParams) {
  await ticketStore.createGuestTicket(data, checkInStore.eventDetailId)
}

async function fetchCheckInSummary() {
  checkInSummary.value = await dashboardStore.fetchCheckInSummary(checkInStore.eventDetailId)
}

async function fetchCheckInList(reset: boolean = false) {
  await checkInStore.fetchCheckInList(reset)
}

async function fetchTicketTypeList() {
  await ticketTypeStore.fetchTicketTypeList()
}

async function exportTicket() {
  startExportLoading()
  const url = await checkInStore.exportTicket(checkedRowKeysRef.value)
  endExportLoading()

  if (!url) {
    window.$message.error('Failed to fetch the CSV url')
    return
  }
  window.location.href = url
}

async function handleScanSuccess(decodedText: any) {
  const params: Entity.CheckIn.CreateParams = {
    eventDetailId: selectedEventDetailId.value || 0,
    ticketNumber: decodedText[0].rawValue,
  }
  const data = await checkInStore.createCheckIn(params)
  if (data) {
    lastCheckInTicket.value = data
    await fetchCheckInList()
    resultWindowStore.postToResultPage({
      ticket: lastCheckInTicket.value,
      showCustomNote: showCustomNote.value,
    })
  }
}

async function handleScannerKeydown(e) {
  if (this.scanTimeout) {
    clearTimeout(this.scanTimeout)
  }

  if (e.key === 'Enter') {
    if (this.scanBuffer.startsWith('TICKET')) {
      checkInStore.formModel.ticketNumber = this.scanBuffer
      try {
        await createCheckIn()
      }
      catch (e) {
        this.scanBuffer = ''
        return e
      }
    }
    this.scanBuffer = ''
    return
  }

  if (e.key.length === 1) {
    this.scanBuffer += e.key
  }

  this.scanTimeout = setTimeout(() => {
    this.scanBuffer = ''
  }, 200)
}

function handleScanError(error: any) {
  console.error('Scan error: ', error)
}

// function openResultPage() {
//   if (!resultWindow || resultWindow.closed) {
//     resultWindow = window.open('/#/check-in-results', '_blank')
//   }
//   else {
//     resultWindow.focus()
//   }
// }

function openResultPage() {
  if (!resultWindowStore.resultWindow || resultWindowStore.resultWindow.closed) {
    const win = window.open('/#/check-in-results', '_blank')
    if (win)
      resultWindowStore.setResultWindow(win)
  }
  else {
    resultWindowStore.resultWindow.focus()
  }
}

function setModalType(type: ModalType) {
  modalType.value = type
}
function handleAddTable() {
  setModalType('add')
  openFormModal()
}

const columns: DataTableColumns<Entity.CheckIn> = [
  {
    type: 'selection',
    options: [
      'all',
      'none',
    ],
  },
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row: Entity.CheckIn, index: number) {
      return index + 1 + ((checkInStore.currentPage - 1) * checkInStore.currentSize)
    },
  },
  { title: $t('ticket.ticketNumber'), align: 'center', key: 'ticket.ticketNumber' },
  { title: $t('ticket.name'), align: 'center', key: 'ticket.holderName' },
  { title: $t('ticket.email'), align: 'center', key: 'ticket.email' },
  { title: $t('ticket.contactNumber'), align: 'center', key: 'ticket.contactNumber' },
  { title: $t('ticket.companyName'), align: 'center', key: 'ticket.companyName' },
  {
    title: $t('ticket.checkInAt'),
    align: 'center',
    key: 'createdAt',
    render(row: Entity.CheckIn) {
      return formatDate(row.createdAt)
    },
  },
  {
    title: $t('common.action'),
    align: 'center',
    key: 'actions',
    render(row: Entity.CheckIn) {
      return (
        <NSpace justify="center">
          <NButton size="small" onClick={() => viewTicket(row.ticket.ticketNumber)}>
            View
          </NButton>
          <NPopconfirm onPositiveClick={() => removeCheckIn(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton size="small">
                  Remove
                </NButton>
              ),
            }}
          </NPopconfirm>
        </NSpace>
      )
    },
  },
]

// function handleEnter(event: any) {
//   event.preventDefault()
//   if (submitButton.value && typeof submitButton.value.$el.click === 'function') {
//     submitButton.value.$el.click()
//   }
//   else {
//     console.error('Button click method not available')
//   }
// }

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleScannerKeydown)
})

onBeforeMount(() => {
  checkInStore.eventDetailId = Number(route.query.eventDetailId)
  ticketStore.eventId = Number(route.query.eventId)
  eventDetailName.value = route.query.eventDetailName ? route.query.eventDetailName.toString() : ''
  ticketTypeStore.eventId = Number(route.query.eventId)
})

onMounted(async () => {
  window.addEventListener('keydown', handleScannerKeydown)
  selectedEventDetailId.value = Number(route.query.eventDetailId)

  await fetchCheckInSummary()
  await fetchTicketTypeList()
  startLoading()
  await fetchCheckInList()
  endLoading()

  const pusher = new Pusher('221df05455b839177af5', {
    cluster: 'ap1',
  })

  const channel = pusher.subscribe(`checkin-event-detail-${checkInStore.eventDetailId}`)
  channel.bind('new-check-in-ticket-event', (data: any) => {
    window.$notification.success({
      title: 'A guest has checked-in!',
      content: `
      ${data.ticketNumber}
      ${data.holderName}
      ${data.email}
      ${data.contactNumber}
      ${data.companyName}
      `,
      duration: 4000,
      keepAliveOnHover: true,
    })
    fetchCheckInList()
    fetchCheckInSummary()
  })
})
</script>

<template>
  <div class="px-8 pb-20">
    <h1 class="text-4xl fw-bold text-theme my-8">
      {{ eventDetailName }}
    </h1>

    <div class="flex flex-col xl:flex-row gap-6 max-w-100vw">
      <n-card class="min-h-400px xl:min-w-100 w-full flex-1 xl:max-w-100 rounded-theme shadow" :bordered="false" content-style="padding:12px;">
        <template v-if="!scannerVisible">
          <!-- <n-input-group class="mb-4">
            <NButton ref="submitButton" tertiary type="primary" class="flex-1 bg-transparent" size="large" @click="createCheckIn">
              <nova-icon icon="formkit:submit" :size="24" class="text-theme" />
            </NButton>

            <n-input :ref="ticketInput" v-model:value="checkInStore.formModel.ticketNumber" class="bg-[#f2f2f2] text-muted" size="large" placeholder="Ticket Number" clearable autofocus @keydown.enter="handleEnter" />
          </n-input-group> -->
        </template>

        <template v-if="lastCheckInTicket">
          <n-card class="inner-card" content-style="padding:12px; border: 1px solid #ddd; border-radius: 4px;">
            <div>
              <strong>Ticket Number:</strong> {{ lastCheckInTicket.ticketNumber }}
            </div>
            <div>
              <strong>Name:</strong> {{ lastCheckInTicket.holderName }}
            </div>
            <div>
              <strong>Email:</strong> {{ lastCheckInTicket.email }}
            </div>
            <div>
              <strong>Contact Number:</strong> {{ lastCheckInTicket.contactNumber }}
            </div>
            <div>
              <strong>Company Name:</strong> {{ lastCheckInTicket.companyName }}
            </div>
            <div>
              <strong>Table:</strong> {{ lastCheckInTicket.tableSeats[0]?.tableInfo.name || 'N/A' }}
            </div>
            <div v-for="(note, index) in lastCheckInTicket?.customNote" :key="index" :label="note.key">
              <strong>{{ note.key }}:</strong> {{ note.value }}
            </div>
          </n-card>
        </template>

        <template v-if="scannerVisible">
          <div class="flex-grow flex flex-col justify-end mt-4">
            <div ref="scannerOverlay" class="scanner-overlay">
              <QrcodeStream
                :paused="false"
                @error="handleScanError"
                @detect="handleScanSuccess"
              />
              <NButton type="default" class="mt-4" @click="closeScanner">
                Close Scanner
              </NButton>
            </div>
          </div>
        </template>
      </n-card>

      <NSpace size="large" vertical class="flex-1 overflow-auto shadow">
        <n-card class="rounded-theme shadow" :bordered="false">
          <div class="flex flex-row mb-4 gap-4 items-center">
            <n-progress
              :border-radius="10"
              :height="40"
              color="#7f9a66"
              type="line"
              :percentage="Math.round(((checkInSummary?.totalCheckIn || 0) / (checkInSummary?.totalTicket || 0)) * 100 * 100) / 100 || 0"
              indicator-placement="inside"
              processing
            />

            <span style="white-space: nowrap;" class="text-lg text-muted">
              {{ `${checkInSummary?.totalTicket || 0} Tickets` }}
            </span>
          </div>

          <NSpace vertical size="large">
            <div class="flex flex-wrap gap-4 items-center">
              <n-switch v-model:value="showCustomNote" size="large" class="mt-1">
                <template #checked>
                  Custom Note
                </template>
                <template #unchecked>
                  Custom Note
                </template>
              </n-switch>

              <n-input-group class="sm:min-w-80 w-auto">
                <n-input v-model:value="checkInStore.searchKeyword" :placeholder="`${$t('common.search')} ${$t('common.keyword')}`" clearable @keyup.enter="fetchCheckInList(true)">
                  <template #prefix>
                    <div>
                      <icon-park-outline-search />
                    </div>
                  </template>
                </n-input>
              </n-input-group>

              <NButton type="default" @click="openScanner">
                <template #icon>
                  <icon-park-outline-camera />
                </template>
                Open Scanner Mode
              </NButton>

              <NButton type="default" @click="handleAddTable">
                <template #icon>
                  <icon-park-outline-add-one />
                </template>
                Add Walk In Guest
              </NButton>
              <NButton type="default" @click="openResultPage">
                <template #icon>
                  <Icon icon="mdi:tv" />
                </template>
                Launch Display Tab
              </NButton>

              <NButton type="default" @click="openManualCheckInDrawer">
                <template #icon>
                  <icon-park-outline-check-in />
                </template>
                Manual Check-In
              </NButton>

              <NButton ghost :loading="exportLoading" @click="exportTicket">
                <template #icon>
                  <Icon icon="uil:export" />
                </template>
                Export
              </NButton>
            </div>

            <div style="white-space: pre;" class="mt-2 min-h-[680px]">
              <n-data-table
                v-model:checked-row-keys="checkedRowKeysRef"
                :row-key="(row) => row.id"
                :columns="columns"
                :data="checkInStore.checkInList"
                :loading="loading"
                :bordered="false"
                class="shadow rounded-theme"
              />
            </div>

            <Pagination
              :count="checkInStore.totalItems"
              :page="checkInStore.currentPage"
              :page-size="checkInStore.currentSize"
              @change="checkInStore.handlePageChange"
            />
          </NSpace>
        </n-card>
      </NSpace>

      <ManualCheckInDrawer
        v-model:visible="manualCheckInDrawerVisible"
        :show-custom-note="showCustomNote"
      />

      <TableModal
        v-model:visible="formModalVisible"
        :type="modalType"
        :on-create="handleCreate"
      />
    </div>
  </div>
</template>

<style scoped>
.scanner-overlay {
  width: 100%;
  height: auto;
  border-radius: 8px;
}
</style>
