<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui'
import { NButton, NSpace } from 'naive-ui'
import { Icon } from '@iconify/vue'
import { $t } from '@/utils'
import { useTicketStore } from '@/store/ticket'
import { useBoolean } from '@/hooks'
import { useCheckInStore } from '@/store/check-in'
import { useResultWindowStore } from '@/store/result-window'

// Interface declaration
interface Props {
  visible: boolean
  showCustomNote: boolean
}
interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const ticketList = ref<Entity.Ticket[]>()
const filteredTicketList = ref<Entity.Ticket[]>()

const ticketStore = useTicketStore()
const checkInStore = useCheckInStore()
const resultWindowStore = useResultWindowStore()

const selectedTicket = ref<Entity.Ticket>()
const selectedEventDetailId = ref<number>()

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: confirmModalVisible, setTrue: openConfirmModal, setFalse: closeConfirmModal } = useBoolean(false)
const { bool: exportLoading, setTrue: startExportLoading, setFalse: endExportLoading } = useBoolean(false)

const columns: DataTableColumns<Entity.Ticket> = [
  { title: $t('ticket.name'), align: 'center', key: 'holderName' },
  { title: $t('ticket.companyName'), align: 'center', key: 'companyName', className: 'max-w-200px overflow-ellipsis' },
  // { title: $t('ticket.contactNumber'), align: 'center', key: 'contactNumber' },
  // { title: $t('ticket.ticketType'), align: 'center', key: 'ticketType.name' },
  {
    title: $t('common.action'),
    align: 'center',
    key: 'actions',
    render(row: Entity.Ticket) {
      return (
        <NSpace justify="center">
          <NButton size="small" onClick={handleCheckIn(row)} secondary type="default" class="shadow bg-white">
            Check In
          </NButton>
        </NSpace>
      )
    },
  },
]

async function exportTicket() {
  startExportLoading()
  const url = await checkInStore.exportTicket([], false)
  endExportLoading()

  if (!url) {
    window.$message.error('Failed to fetch the CSV url')
    return
  }
  window.location.href = url
}

function handleCheckIn(row: Entity.Ticket) {
  return () => {
    openConfirmModal()
    selectedTicket.value = row
  }
}

function filterCheckInTicketList() {
  if (ticketStore.searchKeyword && !ticketStore.searchKeyword.trim()) {
    filteredTicketList.value = ticketList.value
  }
  else {
    filteredTicketList.value = ticketList.value?.filter((item) => {
      return (
        item.holderName.toLowerCase().includes(ticketStore.searchKeyword?.toLowerCase() || '')
        || item.companyName.toLowerCase().includes(ticketStore.searchKeyword?.toLowerCase() || '')
      )
    })
  }
}

async function fetchCheckInTicketList() {
  startLoading()
  ticketStore.eventDetailId = selectedEventDetailId.value || 0
  ticketList.value = await ticketStore.fetchCheckInTicketList()
  filteredTicketList.value = ticketList.value
  endLoading()
}

async function confirmCheckIn() {
  const params: Entity.CheckIn.CreateParams = {
    eventDetailId: selectedEventDetailId.value || 0,
    ticketNumber: selectedTicket.value?.ticketNumber || '',
  }
  const data = await checkInStore.createCheckIn(params)
  if (data) {
    await fetchCheckInTicketList()
    window.$message.success('Manual check in successfully!')
    resultWindowStore.postToResultPage({
      ticket: data,
      showCustomNote: props.showCustomNote,
    })
    closeConfirmModal()
  }
}

const drawerVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const route = useRoute()

watch(drawerVisible, async (newValue) => {
  if (newValue) {
    await fetchCheckInTicketList()
  }
})

onMounted(async () => {
  selectedEventDetailId.value = Number(route.query.eventDetailId)
  await fetchCheckInTicketList()
})
</script>

<template>
  <n-drawer
    v-model:show="drawerVisible"
    placement="right"
    :width="700"
    :show-header="true"
    closable
    close-on-esc
    style="border-top-left-radius: 20px; border-bottom-left-radius: 20px"
  >
    <n-drawer-content>
      <n-flex class="mt-4 mb-8" align="center" justify="space-between">
        <div class="text-2xl fw-bold">
          Manual Check-in
        </div>
      </n-flex>
      <n-input-group class="min-w-80 w-auto">
        <n-input v-model:value="ticketStore.searchKeyword" :placeholder="`${$t('common.search')} ${$t('common.keyword')}`" clearable @keyup.enter="filterCheckInTicketList()">
          <template #prefix>
            <div>
              <icon-park-outline-search />
            </div>
          </template>
        </n-input>

        <NButton ghost :loading="exportLoading" @click="exportTicket">
          <template #icon>
            <Icon icon="uil:export" />
          </template>
          Export
        </NButton>
      </n-input-group>
      <div style="white-space: pre;" class="my-24px">
        <n-data-table :columns="columns" :data="filteredTicketList" :loading="loading" :bordered="false" class="shadow rounded-theme" />
      </div>
      <Pagination
        :count="ticketStore.totalItems"
        :page="ticketStore.currentPage"
        :page-size="ticketStore.currentSize"
        @change="ticketStore.handlePageChange"
      />
      <n-modal
        v-model:show="confirmModalVisible"
        title="Manual Check In"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-card style="width: 600px" title="Manual Check In">
          <div>Are you sure want to manually check in this ticket?</div>

          <template #footer>
            <NSpace justify="end">
              <NButton @click="closeConfirmModal">
                Cancel
              </NButton>
              <NButton type="primary" @click="confirmCheckIn">
                Yes, Check In!
              </NButton>
            </NSpace>
          </template>
        </n-card>
      </n-modal>
    </n-drawer-content>
  </n-drawer>
</template>

<style scoped>
</style>
