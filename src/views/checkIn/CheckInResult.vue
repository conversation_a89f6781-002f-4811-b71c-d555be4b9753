<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { NCard, NDescriptions, NDescriptionsItem, NGrid, NGridItem } from 'naive-ui'

const ticket = ref<Entity.Ticket | null>(null)
const showCustomNote = ref<boolean>(true)

interface IProps {
  ticket: Entity.Ticket
  showCustomNote: boolean
}

onMounted(() => {
  window.addEventListener('message', (event) => {
    try {
      const data: IProps = JSON.parse(event.data)
      ticket.value = data.ticket
      showCustomNote.value = data.showCustomNote
    }
    catch (e) {
      console.error('Failed to parse message data:', e)
    }
  })
})
</script>

<template>
  <div v-if="ticket" class="h-100vh flex justify-center">
    <n-flex class="w-120 m-auto">
      <NCard title="Check-In Details" size="large" bordered>
        <n-space vertical>
          <h1 class="text-4xl mb-4">
            Table: {{ ticket.tableSeats[0]?.tableInfo.name }}
          </h1>
          <n-steps size="small" :current="2" status="process" vertical>
            <n-step
              title="Check-In Complete"
              description="You have successfully checked in. Welcome!"
            />
            <n-step
              :title="`Proceed to ${ticket.tableSeats[0]?.tableInfo.name || 'Main Hall'}`"
              :description="`You can now proceed to ${ticket.tableSeats[0]?.tableInfo.name || 'Main Hall'}. Enjoy your visit!`"
            />
          </n-steps>
        </n-space>

        <NGrid cols="1" x-gap="12" y-gap="30" class="mt-8">
          <NGridItem>
            <NDescriptions :column="1" title="Ticket Information" label-placement="left">
              <NDescriptionsItem label="Ticket Number">
                {{ ticket.ticketNumber }}
              </NDescriptionsItem>
            </NDescriptions>
          </NGridItem>
          <NGridItem>
            <NDescriptions :column="1" title="Holder Information" label-placement="left">
              <NDescriptionsItem label="Holder Name">
                {{ ticket.holderName }}
              </NDescriptionsItem>
              <NDescriptionsItem label="Company Name">
                {{ ticket.companyName }}
              </NDescriptionsItem>
              <NDescriptionsItem label="Email">
                {{ ticket.email }}
              </NDescriptionsItem>
              <NDescriptionsItem label="Contact Number">
                {{ ticket.contactNumber }}
              </NDescriptionsItem>
            </NDescriptions>
          </NGridItem>
          <NGridItem v-if="showCustomNote">
            <NDescriptions :column="1" title="Custom Note" label-placement="left">
              <NDescriptionsItem v-for="(note, index) in ticket?.customNote" :key="index" :label="note.key">
                {{ note.value }}
              </NDescriptionsItem>
            </NDescriptions>
          </NGridItem>
        </NGrid>
      </NCard>
    </n-flex>
  </div>
</template>

<style scoped>
</style>
