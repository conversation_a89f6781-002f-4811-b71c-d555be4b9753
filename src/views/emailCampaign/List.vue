<script lang="ts" setup>
import { useEmailCampaignStore } from '@/store/email-campaign'
import SourClick from '@/assets/sour-click.svg'
import { useEventStore } from '@/store/event'
import Lime from '@/assets/lime.svg'

const eventStore = useEventStore()
const emailCampaignStore = useEmailCampaignStore()

function handleCreate() {
  emailCampaignStore.state.formType = 'add'
  emailCampaignStore.ui.openFormModal()
}

function handleOpenDrawer(data: Entity.EmailCampaign) {
  emailCampaignStore.state.selectedData = data
  emailCampaignStore.ui.openDrawer()
}

onBeforeMount(async () => {
  emailCampaignStore.state.eventId = eventStore.selectedEvent?.id || 0

  if (emailCampaignStore.state.eventId === 0)
    return

  await emailCampaignStore.actions.getList()
})
</script>

<template>
  <div class="grid lg:grid-cols-2 2xl:grid-cols-3 gap-12">
    <n-card v-for="emailCampaign in emailCampaignStore.state.dataList" :key="emailCampaign.id" class="col-span-1 rounded-theme shadow h-full cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition" @click="handleOpenDrawer(emailCampaign)">
      <div class="flex flex-col">
        <span class="font-medium text-2xl mb-4">{{ emailCampaign.name }}</span>
        <span class="text-gray text-lg">{{ emailCampaign.description }}</span>
        <n-divider style="margin:20px 0;" />
        <span class="text-xl">{{ emailCampaign.supportEmail }}</span>
      </div>
    </n-card>
    <n-card class="col-span-1 rounded-theme shadow h-full py-4 cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition" content-class="flex items-center justify-center" @click="handleCreate()">
      <n-image :src="Lime" class="absolute top-[-12px] left-[-12px] w-34px object-cover rotate-340" preview-disabled />
      <div class="flex flex-col items-center opacity-[0.7]">
        <img :src="SourClick" alt="" class="max-w-100% sm:h-[60px]">
        <h2 class="text-gray-800 font-semibold text-2xl sm:text-3xl md:text-4xl mt-8 mb-2 text-success text-center">
          NEW EMAIL CAMPAIGN
        </h2>
      </div>
    </n-card>
  </div>
</template>

<style lang="scss" scoped>
</style>
