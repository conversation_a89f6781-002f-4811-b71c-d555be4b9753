<script setup lang="ts">
import CampaignInformation from './components/CampaignInformation.vue'
import EmailLogs from './components/EmailLogs.vue'
import { useEmailCampaignStore } from '@/store/email-campaign'
import Lime from '@/assets/lime.svg'

type Tab = 'email-details' | 'email-logs'

const emailCampaignStore = useEmailCampaignStore()
const selectedTab = ref<Tab>('email-details')

function changeTab(tab: Tab) {
  selectedTab.value = tab
}
</script>

<template>
  <n-drawer
    v-model:show="emailCampaignStore.ui.drawerVisible"
    :mask-closable="true"
    class="!w-80% !sm:w-70% "
    close-on-esc
    style="border-top-left-radius: 25px !important;border-bottom-left-radius: 25px !important;"
  >
    <n-drawer-content>
      <n-card class="rounded-theme vendor-card">
        <div class="flex flex-col lg:flex-row">
          <div class="flex flex-col lg:flex-row flex-1 lg:items-center gap-4">
            <n-image :src="Lime" class="object-cover w-60px" preview-disabled />

            <div class="flex flex-col mt-4 lg:mt-0">
              <div class="flex flex-col lg:flex-row fw-bold flex-1 lg:items-center gap-4">
                <n-ellipsis class="text-2xl ">
                  {{ emailCampaignStore.state.selectedData?.name }}
                </n-ellipsis>
              </div>

              <span class="text-muted mt-4 lg:mt-1 mb-2 text-lg">
                <n-ellipsis>
                  {{ emailCampaignStore.state.selectedData?.description }}
                </n-ellipsis>
              </span>
            </div>
          </div>
        </div>
      </n-card>

      <div class="flex flex-col xl:flex-row min-h-85% gap-6">
        <n-card class="rounded-theme mt-6 xl:mt-8 w-full xl:w-250px xl:min-w-250px vendor-card" content-class="flex flex-col !p-2">
          <div class="w-full flex flex-col gap-y-2">
            <div class="fw-semibold text-[#77976A] cursor-pointer px-6 py-2 rounded-xl text-lg" :class="[selectedTab === 'email-details' ? 'bg-[#7F9A6620]' : '']" @click="changeTab('email-details')">
              Email Details
            </div>
            <div class="fw-semibold text-[#77976A] cursor-pointer px-6 py-2 rounded-xl text-lg" :class="[selectedTab === 'email-logs' ? 'bg-[#7F9A6620]' : '']" @click="changeTab('email-logs')">
              Email Logs
            </div>
          </div>
        </n-card>

        <div class="flex flex-col flex-1 gap-y-6 xl:max-w-[calc(100%-275px)]">
          <template v-if="selectedTab === 'email-details'">
            <CampaignInformation />
          </template>
          <template v-else-if="selectedTab === 'email-logs'">
            <EmailLogs />
          </template>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>
