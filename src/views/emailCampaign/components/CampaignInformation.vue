<script setup lang="tsx">
import { Icon } from '@iconify/vue'
import TicketModal from './TicketModal.vue'
import { useEmailCampaignStore } from '@/store/email-campaign'
import { useTicketStore } from '@/store/ticket'

const ticketStore = useTicketStore()

const emailLayout = ref()
const emailCampaignStore = useEmailCampaignStore()

async function handleDelete() {
  if (!emailCampaignStore.state.selectedData?.id)
    return

  const res = await emailCampaignStore.actions.del(emailCampaignStore.state.selectedData.id)
  if (!res)
    return

  window.$message.success('Deleted successfully!')
  emailCampaignStore.state.selectedData = {} as Entity.EmailCampaign
  emailCampaignStore.ui.closeDrawer()
  emailCampaignStore.actions.getList()
}

function handleSendEmail() {
  emailCampaignStore.ui.openTicketModal()
}

function handleEdit() {
  emailCampaignStore.state.formType = 'edit'
  emailCampaignStore.ui.openFormModal()
}

onMounted(() => {
  emailLayout.value = emailCampaignStore.state.selectedData?.emailLayout
})

onUnmounted(() => {
  emailLayout.value = ''
  ticketStore.template = ''
})
</script>

<template>
  <n-card class="rounded-theme xl:mt-8 vendor-card h-100% overflow-auto" content-class="flex flex-col">
    <div class="flex flex-wrap justify-between gap-y-4">
      <n-flex horizontal align="center" class="text-2xl">
        <Icon icon="streamline:user-identifier-card" class="text-3xl" />
        Campaign Information
      </n-flex>

      <div class="flex items-center gap-4">
        <NButton
          type="primary"
          @click="handleSendEmail"
        >
          Send Email
        </NButton>

        <NButton
          size="small"
          @click="handleEdit"
        >
          <icon-park-outline-edit-two />
        </NButton>

        <NPopconfirm @positive-click="handleDelete">
          <template #trigger>
            <NButton size="small" type="error" ghost>
              <icon-park-outline-delete />
            </NButton>
          </template>
          Confirm Delete?
        </NPopconfirm>
      </div>
    </div>

    <div class="flex flex-col gap-y-6 mt-8">
      <div class="flex flex-col text-xl space-y-2">
        <div class="text-muted">
          Support Email
        </div>
        <div class="fw-500">
          {{ emailCampaignStore.state.selectedData?.supportEmail }}
        </div>
      </div>
    </div>

    <div class="mt-8">
      <div id="ticket-content" v-html="emailLayout" />
    </div>
  </n-card>

  <TicketModal />
</template>
