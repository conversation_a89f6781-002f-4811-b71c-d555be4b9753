<script setup lang="tsx">
import type { DataTableColumns, DataTableInst, DataTableSortState } from 'naive-ui'
import { NButton, NSpace } from 'naive-ui'
import { ref } from 'vue'
import { Icon } from '@iconify/vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { useTicketStore } from '@/store/ticket'
import { useEventStore } from '@/store/event'
import { getSortKey } from '@/utils/default-values'
import { useEmailCampaignStore } from '@/store/email-campaign'
import SelectedDataModal from '@/views/ticketRegistrationApproval/SelectedDataModal.vue'
import SearchModal from '@/views/ticket/SearchModal.vue'
import ConfirmModal from '@/views/ticket/ConfirmModal.vue'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// visibility states
const { bool: selectedDataModalVisible, setTrue: openSelectedDataModal, setFalse: closeSelectedDataModal } = useBoolean(false)
const { bool: searchModalVisible, setTrue: openSearchModal, setFalse: closeSearchModal } = useBoolean(false)
const { bool: confirmModalVisible, setTrue: openConfirmModal, setFalse: closeConfirmModal } = useBoolean(false)

// stores
const eventStore = useEventStore()
const ticketStore = useTicketStore()
const emailCampaignStore = useEmailCampaignStore()

// etc
const eventId = ref<number>(0)
const tableRef = ref<DataTableInst>()
const selectedRowsData = ref<Entity.Ticket[]>([])
const checkedRowKeysRef = ref<Array<string | number>>([])

async function fetchTicketList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await ticketStore.fetchTicketList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handleSorterUpdate(options: DataTableSortState | DataTableSortState[] | null) {
  if (!options)
    return

  const isArray = Array.isArray(options)
  if (!isArray && !options.order) {
    await fetchTicketList(true)
    return
  }

  ticketStore.sort = isArray
    ? options.map(option => ({ column: option.columnKey.toString(), order: getSortKey(option.order) }))
    : [{ column: options.columnKey.toString(), order: getSortKey(options.order) }]

  await fetchTicketList(false)
}

async function handleCriteriaFilter() {
  fetchTicketList(false)
  closeSearchModal()
}

async function handlePageChange(page: number, size: number) {
  startLoading()
  await ticketStore.handlePageChange(page, size)
  endLoading()
}

async function sendEmail() {
  try {
    if (!emailCampaignStore.state.selectedData)
      return

    const selectedTickets = checkedRowKeysRef.value
    if (!selectedTickets.length) {
      window.$message.warning('Please select at least one ticket.')
      return
    }
    window.$loadingBar.start()

    const params: Entity.EmailCampaign.SendEmailParams = {
      eventId: eventId.value,
      ticketIds: selectedTickets.map(Number),
      emailCampaignId: emailCampaignStore.state.selectedData?.id || 0,
    }

    const success = await emailCampaignStore.actions.sendEmail(params)

    if (success) {
      closeSelectedDataModal()
      emailCampaignStore.ui.closeTicketModal()
      window.$message.success('Email sent successfully !')
    }
    window.$loadingBar.finish()
  }
  catch (e) {
    window.$loadingBar.finish()
    window.$message.error(e)
  }
}

const columns: DataTableColumns<Entity.Ticket> = [
  {
    type: 'selection',
    options: [
      'all',
      'none',
    ],
  },
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row: Entity.Ticket, index: number) {
      return index + 1 + ((ticketStore.currentPage - 1) * ticketStore.currentSize)
    },
  },
  { title: $t('ticket.ticketNumber'), align: 'center', key: 'ticketNumber' },
  { title: $t('ticket.name'), align: 'center', key: 'holderName', sorter: true },
  { title: $t('ticket.email'), align: 'center', key: 'email', className: 'max-w-200px overflow-ellipsis', sorter: true },
  { title: $t('ticket.companyName'), align: 'center', key: 'companyName', className: 'max-w-200px overflow-ellipsis', sorter: true },
  { title: $t('ticket.contactNumber'), align: 'center', key: 'contactNumber' },
  {
    title: $t('ticket.ticketType'),
    align: 'center',
    key: 'ticketTypeId',
    sorter: true,
    render(row: Entity.Ticket) {
      return row.ticketType?.name || 'Unknown'
    },
  },
  { title: $t('ticket.totalEmailSent'), align: 'center', key: 'totalEmailSent', sorter: true },
]

watch(checkedRowKeysRef, (newCheckedKeys) => {
  newCheckedKeys.forEach((key) => {
    const row = ticketStore.ticketList.find(item => item.id === key)
    if (row && !selectedRowsData.value.some(item => item.id === row.id)) {
      selectedRowsData.value.push(row)
    }
  })

  selectedRowsData.value = selectedRowsData.value.filter(row => newCheckedKeys.includes(row.id))
})

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0) {
    endLoading()
    return
  }

  ticketStore.eventId = eventId.value
  ticketStore.searchKeyword = null
  await fetchTicketList()
  endLoading()
})

onBeforeMount(() => {
  startLoading()
  eventId.value = eventStore.selectedEvent?.id || 0
  ticketStore.handleResetSearch()
})
</script>

<template>
  <n-modal
    v-model:show="emailCampaignStore.ui.ticketModalVisible"
    :mask-closable="true"
    :segmented="{ content: true, action: true }"
    class="max-h-80vh max-w-80%"
  >
    <NSpace size="large" vertical class="flex-1" style="gap:44px">
      <n-card :border="false" class="rounded-theme min-h-[calc(100vh-1000px)] max-h-80vh overflow-auto">
        <template #header>
          <div class="flex flex-col">
            <div class="flex justify-between">
              <n-flex horizontal align="center">
                <icon-park-outline-mail />
                <b class="uppercase">Select ticket to send</b>
              </n-flex>
            </div>
          </div>
        </template>
        <NSpace vertical size="large">
          <div class="flex flex-wrap gap-4 mb-2">
            <n-input-group class="sm:w-80">
              <n-input v-model:value="ticketStore.searchKeyword" placeholder="Search Keyword" clearable @keyup.enter="fetchTicketList(true)">
                <template #prefix>
                  <div>
                    <icon-park-outline-search />
                  </div>
                </template>
              </n-input>
            </n-input-group>

            <NButton ghost class="ml-a" @click="openSearchModal">
              <template #icon>
                <icon-park-outline-filter />
              </template>
              Filter
            </NButton>

            <NButton type="primary" @click="openSelectedDataModal">
              <template #icon>
                <Icon icon="icon-park-outline:list" />
              </template>
              Send Email
            </NButton>
          </div>

          <div style="white-space: pre;" class="min-h-[400px]">
            <n-data-table
              ref="tableRef"
              v-model:checked-row-keys="checkedRowKeysRef"
              :columns="columns"
              :data="ticketStore.ticketList"
              :loading="loading"
              :bordered="false"
              class="shadow rounded-theme"
              :row-key="(row) => row.id" @update:sorter="handleSorterUpdate"
            />
            <div id="ticket-template" class="hidden" v-html="ticketStore.template" />
          </div>
        </NSpace>

        <div class="flex flex-wrap justify-between mb-14">
          <Pagination
            class="mt-4"
            :count="ticketStore.totalItems"
            :page="ticketStore.currentPage"
            :page-size="ticketStore.currentSize"
            @change="handlePageChange"
          />

          <div class="text-gray-400 mt-4">
            {{ ticketStore.totalItems }} Ticket(s)
          </div>
        </div>

        <SearchModal
          v-model:visible="searchModalVisible"
          :on-search="handleCriteriaFilter"
        />

        <SelectedDataModal
          :columns="columns.filter((_, index) => ![0, 1].includes(index))"
          :show-modal="selectedDataModalVisible"
          :selected-rows-data="selectedRowsData"
          :action="openConfirmModal"
          @update:show-modal="val => selectedDataModalVisible = val"
        />

        <ConfirmModal
          :show-modal="confirmModalVisible"
          title="Send Email"
          message="Are you sure the result in the listing are correctly filtered?"
          confirm-button-text="Yes, Proceed!"
          cancel-button-text="Cancel"
          @confirm="sendEmail"
          @cancel="closeConfirmModal"
          @update:show-modal="val => confirmModalVisible = val"
        />
      </n-card>
    </NSpace>
  </n-modal>
</template>

<style>
.custom-drawer .n-drawer-body-content-wrapper{
  padding:0 !important;
}
</style>
