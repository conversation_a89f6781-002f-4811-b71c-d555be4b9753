<script setup lang="ts">
import { defineProps, ref, watch } from 'vue'
import { type ECOption, useEcharts } from '@/hooks'

// Define the type for the data prop
interface EmailLogData {
  success: {
    total: number
    percent: number
  }
  fail: {
    total: number
    percent: number
  }
}

// Define the incoming data prop
const props = defineProps<{ data: EmailLogData }>()

// Initialize the chart options
const option = ref<ECOption>({
  title: {
    left: 'start',
    top: '10px',
    textStyle: {
      fontSize: 20,
      color: '#333',
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: (params) => {
      const { name } = params
      let total = 0

      switch (name) {
        case 'Success':
          total = props.data.success.total
          break
        case 'Fail':
          total = props.data.fail.total
          break
      }

      return `${name}: <br/> 
              Total: ${total} <br/>`
    },
  },
  toolbox: {
    show: true,
    feature: {
      mark: { show: true },
      dataView: { show: false, readOnly: false },
      restore: { show: false },
    },
  },
  series: [
    {
      name: 'Email Status',
      type: 'pie',
      radius: ['50%', '80%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: false,
      },
      labelLayout: {
        draggable: true,
      },
      data: [
        { value: props.data.success.percent ?? 0, name: 'Success', itemStyle: { color: '#4CAF50' } },
        { value: props.data.fail.percent ?? 0, name: 'Fail', itemStyle: { color: '#F44336' } },
      ],
    },
  ],
})

// Watch for changes in the prop and update the chart
watch(
  () => props.data,
  (newData) => {
    option.value.series[0].data = [
      { value: newData.success.percent ?? 0, name: 'Success', itemStyle: { color: '#7F9A66' } },
      { value: newData.fail.percent ?? 0, name: 'Fail', itemStyle: { color: '#B29F90' } },
    ]
  },
  { immediate: true },
)

const lineRef = ref<HTMLElement | null>(null)
useEcharts(lineRef, option)
</script>

<template>
  <div
    ref="lineRef"
    class="min-h-250px h-250px w-300px"
  />
</template>

<style scoped>
</style>
