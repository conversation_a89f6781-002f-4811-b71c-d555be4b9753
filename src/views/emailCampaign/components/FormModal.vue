<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { $t } from '@/utils/i18n'
import { useEmailCampaignStore } from '@/store/email-campaign'
import { useEmailTemplateStore } from '@/store/email-template'
import { useBoolean } from '@/hooks'

const emailTemplateStore = useEmailTemplateStore()
const emailCampaignStore = useEmailCampaignStore()

const formModel = ref<Entity.EmailCampaign.CreateUpdateParams>(emailCampaignStore.state.defaultFormModel)
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean()

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.edit'),
}[emailCampaignStore.state.formType || 'add']))

function updateFormModelByModalType() {
  formModel.value = {
    name: '',
    description: '',
    emailLayout: '',
    eventId: 0,
    supportEmail: '',
    emailTemplateTypeId: 1,
  }
}

async function handleSubmit() {
  startLoading()
  const res = await (emailCampaignStore.state.formType === 'add'
    ? emailCampaignStore.actions.create(formModel.value as Entity.EmailCampaign.CreateUpdateParams)
    : emailCampaignStore.actions.update(emailCampaignStore.state.selectedData?.id || 0, formModel.value as Entity.EmailCampaign.CreateUpdateParams))
  endLoading()

  if (!res)
    return

  if (emailCampaignStore.state.selectedData) {
    emailCampaignStore.state.selectedData.name = formModel.value.name
    emailCampaignStore.state.selectedData.description = formModel.value.description
    emailCampaignStore.state.selectedData.supportEmail = formModel.value.supportEmail
  }
  window.$message.success('Saved successfully!')
  emailCampaignStore.ui.closeFormModal()
  await emailCampaignStore.actions.getList()
}

watch(() => emailCampaignStore.ui.formModalVisible, (newValue) => {
  if (!newValue) {
    updateFormModelByModalType()
    return
  }

  const layout = emailTemplateStore.dataList.find(item => item.id === 1)?.emailTemplateLayout.layout || 'None'

  if (emailCampaignStore.state.formType === 'edit' && emailCampaignStore.state.selectedData) {
    const { eventId, name, description, emailLayout, supportEmail, emailTemplateTypeId } = emailCampaignStore.state.selectedData
    formModel.value = { eventId, name, description, emailLayout, supportEmail, emailTemplateTypeId }
  }
  else {
    formModel.value.emailLayout = layout
  }
})

onMounted(async () => {
  await emailTemplateStore.getList()
})
</script>

<template>
  <n-modal
    v-model:show="emailCampaignStore.ui.formModalVisible"
    :mask-closable="false"
    :title="title"
    :segmented="{ content: true, action: true }"
  >
    <n-card
      class="w-500px rounded-theme"
    >
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-gift class="mb-1" />
              <b class="uppercase">{{ title }} Email Campaign</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="emailCampaignStore.ui.closeFormModal" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            Easily create and send professional email campaigns to connect with your audience, promote your message, and drive engagement
          </div>
        </n-flex>
      </template>
      <n-form label-placement="top" :model="formModel" label-align="left">
        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item label="Name" path="name">
            <n-input
              v-model:value="formModel.name" show-count
              :maxlength="50"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Description" path="name">
            <n-input
              v-model:value="formModel.description"
              type="textarea" show-count
              :maxlength="255"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item label="Support Email" path="name">
            <n-input
              v-model:value="formModel.supportEmail" show-count
              :maxlength="50"
            />
          </n-form-item-grid-item>
        </n-grid>
      </n-form>
      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="emailCampaignStore.ui.closeFormModal">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" :loading="loading" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
