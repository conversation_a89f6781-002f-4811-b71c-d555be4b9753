<script setup lang="ts">
import { Icon } from '@iconify/vue'
import EmailLogSummaryChart from './EmailLogSummaryChart.vue'
import { useEmailCampaignStore } from '@/store/email-campaign'
import { useEmailCampaignLogStore } from '@/store/email-campaign-log'
import { formatDate } from '@/utils'
import { useBoolean } from '@/hooks'

const emailCampaignStore = useEmailCampaignStore()
const emailCampaignLogStore = useEmailCampaignLogStore()

const { bool: downloading, setTrue: startDownload, setFalse: endDownload } = useBoolean()

async function handleDownload(id: number) {
  if (!emailCampaignStore.state.selectedData)
    return

  startDownload()
  const res = await emailCampaignLogStore.actions.download({ emailLogId: id, emailCampaignId: emailCampaignStore.state.selectedData?.id })
  if (res) {
    const link = document.createElement('a')
    link.href = res.csvFile
    link.download = ''
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  endDownload()
}

onMounted(async () => {
  if (!emailCampaignStore.state.selectedData)
    return

  const params: Entity.EmailCampaignLog.ListParams = {
    emailCampaignId: emailCampaignStore.state.selectedData?.id,
  }
  await emailCampaignLogStore.actions.getList(params)
})
</script>

<template>
  <div class="lg:mt-8">
    <div v-if="emailCampaignLogStore.ui.loading" class="">
      <n-card v-for="i in 1" :key="i" class="rounded-theme vendor-card flex-1 mb-4" content-class="flex flex-col !py-7">
        <n-skeleton text :repeat="1" />
      </n-card>
    </div>

    <div v-else>
      <n-collapse v-for="(item, index) in emailCampaignLogStore.state.dataList" :key="index" class="flex" arrow-placement="right">
        <template #arrow>
          <Icon icon="mdi:chevron-right" class="text-[#A2A7AF] text-[3rem]" />
        </template>

        <template v-if="item.pending">
          <n-card class="rounded-theme xl:w-250px xl:min-w-250px vendor-card flex-1 mb-4" content-class="flex flex-col !py-3">
            <n-collapse-item title="Pending">
              <span>Email logs are currently being processed. Please check back shortly.</span>
            </n-collapse-item>
          </n-card>
        </template>
        <template v-else>
          <n-card class="rounded-theme xl:w-250px xl:min-w-250px vendor-card flex-1 mb-4" content-class="flex flex-col !py-3">
            <template v-if="emailCampaignLogStore.state.dataList.length > 0 && !emailCampaignLogStore.ui.loading">
              <n-collapse-item :title="formatDate(item.sentAt)">
                <div class="grid grid-cols-2 gap-8">
                  <div class="col-span-2 lg:col-span-1">
                    <EmailLogSummaryChart :data="{ fail: item.fail, success: item.success }" />
                  </div>
                  <div class="col-span-2 lg:col-span-1">
                    <div class="flex flex-col gap-y-6 mt-8">
                      <div class="flex flex-col text-xl space-y-2">
                        <div class="text-muted">
                          Support Email
                        </div>
                        <div class="fw-500">
                          {{ emailCampaignStore.state.selectedData?.supportEmail }}
                        </div>
                      </div>
                      <div class="flex flex-col text-xl space-y-2">
                        <div class="text-muted">
                          List of Users
                        </div>
                        <div class="fw-500">
                          <n-button size="large" :loading="downloading" @click="handleDownload(item.id)">
                            <Icon icon="icon-park-outline:file-pdf" class="me-2" />
                            Download CSV Report
                          </n-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </n-collapse-item>
            </template>
            <template v-else>
              <n-empty size="large" description="No data found" />
            </template>
          </n-card>
        </template>
      </n-collapse>
    </div>
  </div>
</template>

<style>
.n-collapse .n-collapse-item .n-collapse-item__header .n-collapse-item__header-main {
  justify-content: space-between !important;
}

.n-collapse .n-collapse-item:not(:first-child){
  border: unset !important;
}
</style>
