<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import { $t } from '@/utils/i18n'
import { useTicketTypeStore } from '@/store/ticket-type'

// Interface declaration
interface Props {
  visible: boolean
  type?: ModalType
  modalData?: Entity.TicketType | null
  onCreate?: (data: Entity.TicketType.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.TicketType.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// End of Type and Interface declaration

const emit = defineEmits<Emits>()

const ticketTypeStore = useTicketTypeStore()
const formModel = ref<Nullable<Entity.TicketType.CreateParams>>(ticketTypeStore.getDefaultFormModel())

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.edit'),
}[props.type || 'add']))

function updateFormModelByModalType() {
  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData }
  }
  else {
    formModel.value = ticketTypeStore.getDefaultFormModel()
  }
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.TicketType.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.TicketType.UpdateParams)
  }

  modalVisible.value = false
}

function handleInput(value) {
  formModel.value.name = value.toUpperCase()
}

watch(() => props.visible, (newValue) => {
  if (newValue)
    updateFormModelByModalType()
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    class="w-500px"
    :segmented="{ content: true, action: true }"
  >
    <n-card class="rounded-theme">
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-ticket />
              <b class="uppercase">{{ title }} Ticket Type</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            Define different ticket categories for your event to give clear options when registering.
          </div>
        </n-flex>
      </template>
      <n-form label-placement="top" :model="formModel" label-align="left">
        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item :label="$t('event.name')" path="name">
            <n-input v-model:value="formModel.name" show-count :maxlength="25" @update:value="handleInput" />
          </n-form-item-grid-item>
        </n-grid>
      </n-form>
      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
