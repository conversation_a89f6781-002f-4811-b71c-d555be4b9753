<script setup lang="tsx">
import { NButton, NCarousel, NSpace } from 'naive-ui'
import { ref } from 'vue'
import ConfirmModal from '../ticket/ConfirmModal.vue'
import TableModal from './TableModal.vue'
import Skeleton from './component/Skeleton.vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { useTicketTypeStore } from '@/store/ticket-type'
import { useEventStore } from '@/store/event'
import { useTicketStore } from '@/store/ticket'
import StackLime from '@/assets/stack-lime.png'
import { renderIcon } from '@/utils'
import { useEventDetailStore } from '@/store/event-detail'
import SourClick from '@/assets/sour-click.svg'
import DefaultSubEventImage from '@/assets/default-sub-event.svg'

// store
const router = useRouter()
const eventStore = useEventStore()
const ticketStore = useTicketStore()
const ticketTypeStore = useTicketTypeStore()
const eventDetailStore = useEventDetailStore()

// loading states
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// visibility states
const { bool: visible, setTrue: openModal, setFalse: closeModal } = useBoolean(false)
const { bool: confirmModalVisible, setTrue: openConfirmModal, setFalse: closeConfirmModal } = useBoolean(false)

// etc
const eventId = ref<number>(0)
const deleteTicketTypeId = ref<number>(0)
const modalType = ref<ModalType>('add')
const options = [
  {
    label: 'Edit',
    key: 'edit',
    icon: renderIcon('icon-park-outline:edit-two'),
  },
  {
    label: 'Delete',
    key: 'delete',
    icon: renderIcon('icon-park-outline:delete'),
  },
]

async function fetchTicketTypeList() {
  await ticketTypeStore.fetchTicketTypeList()
}

async function fetchTicketTypeAssignment() {
  await ticketTypeStore.fetchTicketTypeAssignment()
}

async function handleCreate(data: Entity.TicketType.CreateParams) {
  await ticketTypeStore.createTicketType(data)
  await fetchTicketTypeList()
  await fetchTicketCheckInSummary()
  window.$message.success('Ticket type created successfully!')
  closeModal()
}

async function handleUpdate(data: Entity.TicketType.UpdateParams) {
  await ticketTypeStore.updateTicketType(data)
  await fetchTicketTypeList()
  await fetchTicketCheckInSummary()
  window.$message.success('Ticket type updated successfully!')
  closeModal()
}

async function handleDelete() {
  await ticketTypeStore.deleteTicketType(deleteTicketTypeId.value)
  await fetchTicketCheckInSummary()
  await fetchTicketTypeList()
  window.$message.success('Ticket type deleted successfully!')
  closeConfirmModal()
}

async function fetchTicketCheckInSummary() {
  await ticketStore.fetchTicketCheckInSummary(eventStore.selectedEvent?.id || 0)
}

async function fetchEventDetail() {
  await eventDetailStore.fetchEventDetailList()
}

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleEdit(row: Entity.TicketType) {
  ticketTypeStore.editData = row
  setModalType('edit')
  openModal()
}

function handleAddTable() {
  setModalType('add')
  openModal()
}

function handleRedirectEventDetail(openFormModal = false) {
  router.push({
    path: '/event-details',
    query: openFormModal ? { openFormModal: 'true' } : undefined,
  })
}

function handleSelect(key: string | number, item: Entity.TicketType) {
  if (key === 'edit' && item) {
    handleEdit(item)
  }

  if (key === 'delete' && item) {
    deleteTicketTypeId.value = item.id
    openConfirmModal()
  }
}

watch(() => eventId.value, async (newValue) => {
  if (newValue === 0) {
    endLoading()
    return
  }

  ticketTypeStore.eventId = eventId.value
  eventDetailStore.eventId = eventId.value
  await fetchTicketCheckInSummary()
  await fetchTicketTypeList()
  await fetchTicketTypeAssignment()
  await fetchEventDetail()
  window.$loadingBar.finish()
  endLoading()
})

onBeforeMount(() => {
  startLoading()
  eventId.value = eventStore.selectedEvent?.id || 0
})
</script>

<template>
  <NSpace size="large" vertical class="flex-1">
    <!-- Skeleton -->
    <template v-if="loading">
      <Skeleton />
    </template>

    <!-- Body -->
    <n-grid
      v-else
      :x-gap="44"
      :y-gap="44"
      item-responsive
      cols="10"
      responsive="screen"
    >
      <!-- Left -->

      <n-gi span="10 xl:6" class="h-375px relative z-1">
        <n-image :src="StackLime" class="absolute left-[-10px] w-30px object-cover z-2" preview-disabled />

        <n-card v-if="eventDetailStore.eventDetailList.length < 1" class="rounded-theme shadow h-375px cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition" content-class="flex items-center justify-center" @click="handleRedirectEventDetail(true)">
          <n-flex vertical align="center" class="text-center">
            <n-flex class="mx-auto" align="center">
              <img :src="SourClick" alt="" class="h-[80px] opacity-[0.7]">
            </n-flex>
            <h2 class="text-gray-800 font-bold text-4xl mt-8 mb-2 text-success opacity-[0.7]">
              BUILD AN EXPERIENCE WITH US
            </h2>
          </n-flex>
        </n-card>

        <NCarousel
          v-else
          draggable
          :slides-per-view="eventDetailStore.eventDetailList.length < 3 ? '3' : 'auto'"
          :space-between="20"
          :loop="true"
        >
          <n-carousel-item v-for="item in eventDetailStore.eventDetailList" :key="item.id" class="px-2" style="width:275px;">
            <div class="bg-white p-4 h-340px rounded-theme shadow hover:cursor-pointer" @click="handleRedirectEventDetail()">
              <div class="h-100% flex flex-col">
                <n-image
                  preview-disabled
                  class="flex-1 rounded-theme"
                  :src="item.pictureUrl ?? DefaultSubEventImage"
                  alt="carousel image"
                  object-fit="cover"
                />
                <div class="bg-theme mt-4 px-3 py-2 text-center rounded-lg">
                  <n-ellipsis class="text-white">
                    <span class="fw-semibold text-white">{{ item.name }}</span>
                  </n-ellipsis>
                </div>
              </div>
            </div>
          </n-carousel-item>
        </NCarousel>
      </n-gi>

      <!-- Right -->
      <n-gi span="10 xl:4" class="h-375px">
        <n-card class="rounded-theme shadow h-full" :bordered="false">
          <template #header>
            <div class="flex flex-col sm:flex-row justify-between gap-4">
              <span class="card-title">
                {{ $t('ticket.ticketType') }}
              </span>
              <NButton type="default" @click="handleAddTable">
                <template #icon>
                  <icon-park-outline-add-one />
                </template>
                {{ $t('common.addNew') }}
              </NButton>
            </div>
          </template>

          <NSpace vertical size="large">
            <div style="white-space: pre;">
              <n-scrollbar class="max-h-[240px] sm:max-h-[260px] overflow-hidden pe-6">
                <div class="flex flex-col">
                  <div v-for="item in ticketTypeStore.ticketTypeList" :key="item.id" class="flex flex-row w-full mb-4">
                    <div class="flex-1 text-lg flex items-center">
                      <n-dropdown
                        placement="bottom-start"
                        trigger="click"
                        size="medium"
                        :options="options"
                        @select="(key) => handleSelect(key, item)"
                      >
                        <icon-park-outline-more-one class="hover:cursor-pointer focus:outline-none text-[1.3rem]" />
                      </n-dropdown>
                      <span class="text-[1.3rem] fw-[500]">
                        {{ item.name }}
                      </span>
                    </div>

                    <div>
                      <NSpace justify="center">
                        <div class="flex card-content gap-3 items-center">
                          <div class="fw-medium">
                            {{ ticketTypeStore.ticketAssignment.list.find((ticketTypeItem) => ticketTypeItem.name === item.name)?.total || 0 }}
                          </div>
                          <icon-park-outline-ticket />
                        </div>
                      </NSpace>
                    </div>
                  </div>
                </div>
              </n-scrollbar>
            </div>
          </NSpace>
        </n-card>
      </n-gi>
    </n-grid>

    <TableModal
      v-model:visible="visible"
      :type="modalType"
      :modal-data="ticketTypeStore.editData"
      :on-create="handleCreate"
      :on-update="handleUpdate"
    />

    <ConfirmModal
      v-model:show-modal="confirmModalVisible"
      title="Delete Item"
      message="Are you sure you want to delete this item?"
      @confirm="handleDelete"
      @cancel="closeConfirmModal"
    />
  </NSpace>
</template>

<style>
  .n-carousel .n-carousel__dots.n-carousel__dots--dot .n-carousel__dot {
      background-color: #555;
  }

 .n-carousel__dot.n-carousel__dot--active {
      background-color: #000 !important;
  }

  .n-carousel.n-carousel--bottom .n-carousel__dots{
    bottom:5px !important;
  }
</style>
