<script setup lang="tsx">
</script>

<template>
  <n-grid
    :x-gap="44"
    :y-gap="30"
    cols="10"
    item-responsive
    responsive="screen"
  >
    <n-gi span="10 xl:6" class="h-375px relative z-1">
      <NCarousel
        :loop="true"
        :space-between="10"
        slides-per-view="auto"
      >
        <n-carousel-item v-for="item in [0, 1, 2, 3]" :key="item" class="px-2" style="width:275px">
          <div class="bg-white p-4 h-340px rounded-theme shadow">
            <div class="h-100% flex flex-col">
              <div
                class="flex-1 object-cover rounded-theme"
              >
                <n-skeleton width="100%" class="my-4" :repeat="8" />
              </div>
              <n-skeleton width="100%" class="my-4" />
            </div>
          </div>
        </n-carousel-item>
      </NCarousel>
    </n-gi>
    <n-gi span="10 xl:4" class="h-375px">
      <n-card :bordered="false" class="rounded-theme shadow h-full">
        <template #header>
          <n-skeleton class="w-70%" />
        </template>
        <NSpace vertical size="large" class="mt-14px">
          <n-skeleton class="w-100% mb-3" :repeat="11" />
        </NSpace>
      </n-card>
    </n-gi>
  </n-grid>
</template>
