<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import { $t } from '@/utils/i18n'
import { useLuckyDrawStore } from '@/store/lucky-draw'
import { useTicketTypeStore } from '@/store/ticket-type'

// Interfarce declaration
interface Props {
  visible: boolean
  type?: ModalType
  onCreate?: (data: Entity.LuckyDraw.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.LuckyDraw.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// End of Type and Interface declaration

const emit = defineEmits<Emits>()
const luckyDrawStore = useLuckyDrawStore()
const ticketTypeStore = useTicketTypeStore()

const formModel = ref<Nullable<Entity.LuckyDraw.CreateParams>>(luckyDrawStore.defaultFormModel)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.edit'),
}[props.type || 'add']))

function updateFormModelByModalType() {
  formModel.value = {
    name: null,
    description: null,
    eventDetailId: null,
    winnerQuantity: null,
    isRepeatable: false,
    ticketTypeIds: [],
  }
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    modalVisible.value = false
    await props.onCreate(formModel.value as Entity.LuckyDraw.CreateParams)
  }
}

const ticketTypeOptions = computed(() =>
  (ticketTypeStore.ticketTypeList.map(ticketType => ({
    label: `${ticketType.name}`,
    value: ticketType.id,
  }))) || [],
)

const route = useRoute()

onMounted(async () => {
  ticketTypeStore.eventId = Number(route.query.eventId)
  await ticketTypeStore.fetchTicketTypeList()
})

watch(() => props.visible, (newValue) => {
  if (newValue)
    updateFormModelByModalType()
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    :title="title"
    :segmented="{ content: true, action: true }"
  >
    <n-card
      class="w-500px rounded-theme"
    >
      <template #header>
        <n-flex vertical>
          <n-flex horizontal align="center" justify="space-between">
            <n-flex horizontal align="center">
              <icon-park-outline-gift class="mb-1" />
              <b class="uppercase">{{ title }} Lucky Draw</b>
            </n-flex>
            <icon-park-outline-close-small class="cursor-pointer" @click="modalVisible = false" />
          </n-flex>
          <div class="text-sm text-[#707070]">
            Manage your own Lucky Draw with ease! Set up your prizes, select winners, and add excitement to your event with just a few clicks.
          </div>
        </n-flex>
      </template>
      <n-form label-placement="top" :model="formModel" label-align="left">
        <n-grid cols="1 s:2" x-gap="18" responsive="screen">
          <n-form-item-grid-item :label="$t('luckyDraw.name')" path="name">
            <n-input
              v-model:value="formModel.name" show-count
              :maxlength="50"
            />
          </n-form-item-grid-item>

          <n-form-item-grid-item :label="$t('luckyDraw.winnerQuantity')" path="winnerQuantity">
            <n-input-number v-model:value="formModel.winnerQuantity" class="flex-1" />
          </n-form-item-grid-item>
        </n-grid>
        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item :label="$t('luckyDraw.description')" path="description">
            <n-input v-model:value="formModel.description" type="textarea" />
          </n-form-item-grid-item>

          <n-form-item-grid-item :label="$t('event.ticketType')" path="ticketTypeIds">
            <n-select
              v-model:value="formModel.ticketTypeIds"
              multiple
              filterable
              :placeholder="$t('common.select')"
              :options="ticketTypeOptions"
            />
          </n-form-item-grid-item>
        </n-grid>

        <n-grid cols="1" x-gap="18" responsive="screen">
          <n-form-item-grid-item :label="$t('luckyDraw.isRepeatable')" path="isRepeatable">
            <n-radio-group v-model:value="formModel.isRepeatable">
              <n-radio :value="false">
                No
              </n-radio>
              <n-radio :value="true">
                Yes
              </n-radio>
            </n-radio-group>
          </n-form-item-grid-item>
        </n-grid>
      </n-form>
      <template #footer>
        <n-flex horizontal>
          <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
            Cancel
          </n-button>
          <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="handleSubmit">
            Submit
          </n-button>
        </n-flex>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
