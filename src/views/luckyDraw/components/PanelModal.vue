<script setup lang="ts">
import { computed, defineProps, ref } from 'vue'
import { useLuckyDrawStore } from '@/store/lucky-draw'
import { useTicketStore } from '@/store/ticket'

// Interface declarations
interface Props {
  visible?: boolean
  modalData?: Entity.LuckyDraw | null
}

const props = defineProps<Props>()
const ticketList = ref<Entity.Ticket[]>([])
const winners = ref<Entity.Ticket[] | Entity.LuckyDraw.Winner[]>([])
const pastWinnersList = ref<Entity.Ticket[] | Entity.LuckyDraw.Winner[]>([])
const selectedTickets = ref([])
const onlyCheckedIn = ref<boolean>(false)
let resultWindow: Window | null = null
const windowName: string = 'resultWindow'

// Use the lucky draw store
const luckyDrawStore = useLuckyDrawStore()
const ticketStore = useTicketStore()

const route = useRoute()

async function fetchTicketList() {
  ticketStore.eventId = Number(route.query.eventId)
  ticketList.value = await ticketStore.fetchTicketListWithoutPagination()
}

// Function to draw winners
async function drawWinners() {
  const params: Entity.LuckyDraw.Winner.CreateParams = {
    luckyDrawId: props.modalData?.id || 0,
    isCheckedIn: onlyCheckedIn.value,
  }
  await luckyDrawStore.drawWinners(params)
  winners.value = luckyDrawStore.newWinners

  await pastWinners()
  selectedTickets.value = []
}

async function latestWinners() {
  const params: Entity.LuckyDraw.Winner.HistoryParams = {
    luckyDrawId: props.modalData?.id || 0,
    isActive: true,
  }
  winners.value = await luckyDrawStore.getHistoryWinners(params)
}

async function pastWinners() {
  const params: Entity.LuckyDraw.Winner.HistoryParams = {
    luckyDrawId: props.modalData?.id || 0,
    isActive: false,
  }
  pastWinnersList.value = await luckyDrawStore.getHistoryWinners(params)
}

function openResultPage(): void {
  if (!resultWindow || resultWindow.closed || !resultWindow.location.href.includes('/#/draw-results')) {
    resultWindow = window.open('/#/draw-results', windowName) || null
    if (resultWindow) {
      localStorage.setItem('resultWindow', windowName)
    }
  }
  else {
    resultWindow.focus()
  }
}

function restoreResultWindow(): void {
  const storedWindowName: string | null = localStorage.getItem('resultWindow')
  if (storedWindowName) {
    resultWindow = window.open('', storedWindowName) || null

    if (resultWindow && !resultWindow.closed && resultWindow.location.href.includes('/#/draw-results')) {
      resultWindow.focus()
    }
    else {
      resultWindow = null // Ensure the resultWindow is null if it’s invalid
    }
  }
}

function handleResultWindow() {
  restoreResultWindow()

  if (!resultWindow || resultWindow.closed) {
    // Notify the user to open the result page manually
    window.$message.error('Result window is not open. Please open the result page first.')
  }
}

function displayWinners() {
  handleResultWindow() // Ensure the result window is valid before proceeding

  if (resultWindow && !resultWindow.closed) {
    const plainArray = Array.from(selectedTickets.value)

    const filteredWinners = ticketList.value
      .filter(winner => plainArray.includes(winner.id))
      .map(winner => ({
        ticketId: winner.id,
        holderName: winner.holderName,
        ticketNumber: winner.ticketNumber,
      }))

    if (filteredWinners.length < 1) {
      window.$message.error('Please select at least one winner!')
    }

    resultWindow.postMessage(filteredWinners, '*')
  }
}

function resetCountdown() {
  handleResultWindow() // Ensure the result window is valid before proceeding

  if (resultWindow && !resultWindow.closed) {
    resultWindow.postMessage('resetCountdown', '*')
  }
}

window.addEventListener('load', restoreResultWindow)

const ticketTypeOptions = computed(() => {
  if (winners.value[0]?.ticket) {
    return winners.value.map(ticket => ({ label: ticket.ticket.holderName, value: ticket.ticket.id }))
  }
  else {
    return winners.value.map(ticket => ({ label: ticket.holderName, value: ticket.id }))
  }
})

onMounted(async () => {
  await fetchTicketList()
  await pastWinners()
  await latestWinners()
})
</script>

<template>
  <div class="flex flex-col">
    <n-flex justify="left">
      <n-tooltip trigger="hover">
        <template #trigger>
          <n-button type="default" class="bg-white" @click="drawWinners">
            Generate Winner(s)
          </n-button>
        </template>
        Use this to generate your Lucky Draw Winners ! Every time you click onto this button it will generate a new set of winners for this particular lucky draw
      </n-tooltip>
      <n-tooltip trigger="hover">
        <template #trigger>
          <n-button type="default" class="bg-white" @click="displayWinners">
            Run Lucky Draw
          </n-button>
        </template>
        With this, select a winner you’d like to display on the Result Page and click onto this  button to display that particular winner on the Result Page. You are able to select a single winner or multiple winners
      </n-tooltip>
      <n-tooltip trigger="hover">
        <template #trigger>
          <n-button type="default" class="bg-white" @click="openResultPage">
            Open Result Page
          </n-button>
        </template>
        Use this to open a new tab that allows you to show the Results Page and be ready to Display your winners !
      </n-tooltip>
      <n-tooltip trigger="hover">
        <template #trigger>
          <n-button type="default" class="bg-white" @click="resetCountdown">
            Reset Countdown
          </n-button>
        </template>
        Use this to reset the countdown timer on the Result Page
      </n-tooltip>
    </n-flex>

    <n-radio-group v-model:value="onlyCheckedIn" class="mt-4">
      <n-radio :value="false">
        All tickets
      </n-radio>
      <n-radio :value="true">
        Only Checked-In Tickets
      </n-radio>
    </n-radio-group>

    <n-select
      v-model:value="selectedTickets"
      class="mt-4"
      multiple
      :options="ticketTypeOptions"
      placeholder="Select a winner"
    />
    <n-card title="History Winners" class="mt-4 flex-grow">
      <n-virtual-list style="max-height: 240px" :item-size="42" :items="pastWinnersList">
        <template #default="{ item }">
          <n-space
            :key="item.key"
            vertical
          >
            <div class="mt-4">
              <span>Name: </span>
              <span>{{ item.ticket.holderName }}</span>
              <br>
              <span>Attempt: </span>
              <span>{{ item.attempt }}</span>
            </div>
          </n-space>
        </template>
      </n-virtual-list>
    </n-card>
  </div>
</template>
