<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import PanelModal from './PanelModal.vue'
import { $t } from '@/utils/i18n'
import { useLuckyDrawStore } from '@/store/lucky-draw'
import { useTicketTypeStore } from '@/store/ticket-type'
import { useBoolean } from '@/hooks'
import { useAppStore } from '@/store'

// Interfarce declaration
interface Props {
  visible: boolean
  type?: ModalType
  modalData?: Entity.LuckyDraw | null
  onCreate?: (data: Entity.LuckyDraw.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.LuckyDraw.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// End of Type and Interface declaration

const emit = defineEmits<Emits>()
const luckyDrawStore = useLuckyDrawStore()
const ticketTypeStore = useTicketTypeStore()
const { bool: editModalVisible, setTrue: openEditModal, setFalse: closeEditModal } = useBoolean(false)

const formModel = ref<Nullable<Entity.LuckyDraw.CreateParams>>(luckyDrawStore.defaultFormModel)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.edit'),
}[props.type || 'add']))

function updateFormModelByModalType() {
  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData }
    formModel.value.ticketTypeIds = props.modalData.luckyDrawEligibleGroups.map(group => group.ticketType.id)
  }
  else {
    formModel.value = {
      name: null,
      description: null,
      eventDetailId: null,
      winnerQuantity: null,
      isRepeatable: false,
      ticketTypeIds: [],
    }
  }
}

const appStore = useAppStore()

async function handleDelete(id: number) {
  await luckyDrawStore.deleteLuckyDraw(id)
  appStore.reloadPage()
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.LuckyDraw.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.LuckyDraw.UpdateParams)
  }

  closeEditModal()
  modalVisible.value = false
}

const ticketTypeOptions = computed(() =>
  (ticketTypeStore.ticketTypeList.map(ticketType => ({
    label: `${ticketType.name}`,
    value: ticketType.id,
  }))) || [],
)

const route = useRoute()

onMounted(async () => {
  ticketTypeStore.eventId = Number(route.query.eventId)
  await ticketTypeStore.fetchTicketTypeList()
})

watch(() => props.visible, (newValue) => {
  if (newValue)
    updateFormModelByModalType()
})
</script>

<template>
  <n-drawer
    v-model:show="modalVisible"
    :mask-closable="true"
    preset="card"
    :title="title"
    :width="550"
    :segmented="{ content: true, action: true }"
    :show-header="true"
    close-on-esc
    style="border-top-left-radius: 20px; border-bottom-left-radius: 20px"
    class="custom-drawer"
  >
    <n-drawer-content>
      <n-flex vertical class="my-4 p-8">
        <n-flex vertical>
          <div class="flex justify-between items-center">
            <div class="fw-semibold text-2xl">
              {{ modalData?.name }}
            </div>
            <div class="ms-auto flex flex-row gap-x-2">
              <NButton type="default" size="small" @click="openEditModal()">
                <icon-park-outline-edit-two />
              </NButton>
              <NPopconfirm @positive-click="handleDelete(Number(modalData?.id))">
                Confirm Delete?
                <template #trigger>
                  <NButton size="small" type="error" ghost>
                    <icon-park-outline-delete />
                  </NButton>
                </template>
              </NPopconfirm>
            </div>
          </div>
          <div class="text-muted">
            <n-ellipsis>{{ modalData?.description }}</n-ellipsis>
          </div>
        </n-flex>
        <n-divider style="margin:0;" />
        <n-flex vertical>
          <div class="text-lg">
            {{ modalData?.winnerQuantity }} Winner(s)
          </div>
          <div class="text-lg">
            {{ modalData?.isRepeatable ? 'Repeatable' : 'No Repeatable' }}
          </div>
          <div class="flex flex-col">
            <span class="text-lg">Eligible Groups</span>
            <div class="flex flex-wrap gap-3 mt-4">
              <div v-for="group in modalData?.luckyDrawEligibleGroups" :key="group.ticketType.id" class="rounded-full bg-white shadow px-5 py-2">
                <span class="text-[#448469] fw-bold">{{ group.ticketType.name }}</span>
              </div>
            </div>
          </div>
        </n-flex>
      </n-flex>

      <PanelModal class="flex-grow bg-gray-100 py-8 px-6 rounded-theme" :modal-data="modalData" />
    </n-drawer-content>

    <n-modal v-model:show="editModalVisible">
      <n-card class="w-500px rounded-theme">
        <template #header>
          <n-flex vertical>
            <n-flex horizontal align="center" justify="space-between">
              <n-flex horizontal align="center">
                <icon-park-outline-gift class="mb-1" />
                <b class="uppercase">{{ title }} Lucky Draw</b>
              </n-flex>
              <icon-park-outline-close-small class="cursor-pointer" @click="closeEditModal()" />
            </n-flex>
            <div class="text-sm text-[#707070]">
              Manage your own Lucky Draw with ease! Set up your prizes, select winners, and add excitement to your event with just a few clicks.
            </div>
          </n-flex>
        </template>
        <n-form label-placement="top" :model="formModel" label-align="left">
          <n-grid cols="1 s:2" x-gap="18" responsive="screen">
            <n-form-item-grid-item :label="$t('luckyDraw.name')" path="name">
              <n-input
                v-model:value="formModel.name" show-count
                :maxlength="50"
              />
            </n-form-item-grid-item>

            <n-form-item-grid-item :label="$t('luckyDraw.winnerQuantity')" path="winnerQuantity">
              <n-input-number v-model:value="formModel.winnerQuantity" class="flex-1" />
            </n-form-item-grid-item>
          </n-grid>
          <n-grid cols="1" x-gap="18" responsive="screen">
            <n-form-item-grid-item :label="$t('luckyDraw.description')" path="description">
              <n-input v-model:value="formModel.description" type="textarea" />
            </n-form-item-grid-item>

            <n-form-item-grid-item :label="$t('event.ticketType')" path="ticketTypeIds">
              <n-select
                v-model:value="formModel.ticketTypeIds"
                multiple
                filterable
                :placeholder="$t('common.select')"
                :options="ticketTypeOptions"
              />
            </n-form-item-grid-item>
          </n-grid>

          <n-grid cols="1" x-gap="18" responsive="screen">
            <n-form-item-grid-item :label="$t('luckyDraw.isRepeatable')" path="isRepeatable">
              <n-radio-group v-model:value="formModel.isRepeatable">
                <n-radio :value="false">
                  No
                </n-radio>
                <n-radio :value="true">
                  Yes
                </n-radio>
              </n-radio-group>
            </n-form-item-grid-item>
          </n-grid>
        </n-form>
        <template #footer>
          <n-flex horizontal>
            <n-button type="default" class="shadow flex-1" @click="modalVisible = false">
              Cancel
            </n-button>
            <n-button type="primary" :bordered="false" class="bg-theme text-white shadow flex-1" @click="handleSubmit">
              Submit
            </n-button>
          </n-flex>
        </template>
      </n-card>
    </n-modal>

    <template #action>
      <n-space justify="center">
        <n-button @click="modalVisible = false">
          Cancel
        </n-button>
        <n-button type="primary" @click="handleSubmit">
          Submit
        </n-button>
      </n-space>
    </template>
  </n-drawer>
</template>

<style scoped></style>

<style>
.custom-drawer .n-drawer-body-content-wrapper{
  display:flex;
  flex-direction: column;
  padding:0 !important;
}
</style>
