<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui'
import { NButton, NPopconfirm, NSpace, NTooltip } from 'naive-ui'
import TableModal from './TableModal.vue'
import SearchModal from './SearchModal.vue'
import PanelModal from './PanelModal.vue'
import { useBoolean } from '@/hooks'
import { $t } from '@/utils/i18n'
import { useLuckyDrawStore } from '@/store/lucky-draw'
import { useTicketStore } from '@/store/ticket'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: visible, setTrue: openModal } = useBoolean(false)
const { bool: searchModalVisible, setTrue: openSearchModal, setFalse: closeSearchModal } = useBoolean(false)
const { bool: panelModalVisible, setTrue: openPanelModal } = useBoolean(false)

const luckyDrawStore = useLuckyDrawStore()
const ticketStore = useTicketStore()

const modalType = ref<ModalType>('add')
const selectedLuckyDraw = ref()

function setModalType(type: ModalType) {
  modalType.value = type
}

function handlePanel(row: Entity.LuckyDraw) {
  selectedLuckyDraw.value = row
  openPanelModal()
}

function handleEdit(row: Entity.LuckyDraw) {
  luckyDrawStore.editData = row
  setModalType('edit')
  openModal()
}

function handleAddTable() {
  setModalType('add')
  openModal()
}

async function fetchLuckyDrawList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await luckyDrawStore.fetchLuckyDrawList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handleCreate(data: Entity.LuckyDraw.CreateParams) {
  await luckyDrawStore.createLuckyDraw(data)
  luckyDrawStore.handleResetSearch()
  fetchLuckyDrawList(true)
}

async function handleUpdate(data: Entity.LuckyDraw.UpdateParams) {
  await luckyDrawStore.updateLuckyDraw(data)
  luckyDrawStore.handleResetSearch()
  fetchLuckyDrawList(true)
}

async function handleDelete(id: number) {
  await luckyDrawStore.deleteLuckyDraw(id)
  luckyDrawStore.handleResetSearch()
  fetchLuckyDrawList(true)
}

async function handleCriteriaFilter() {
  fetchLuckyDrawList(false)
  closeSearchModal()
}

const columns: DataTableColumns<Entity.LuckyDraw> = [
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row, index) {
      return index + 1 + ((luckyDrawStore.currentPage - 1) * luckyDrawStore.currentSize)
    },
  },
  { title: $t('luckyDraw.name'), align: 'center', key: 'name' },
  { title: $t('luckyDraw.description'), align: 'center', key: 'description', className: 'max-w-200px overflow-ellipsis' },
  { title: $t('luckyDraw.winnerQuantity'), align: 'center', key: 'winnerQuantity' },
  { title: $t('luckyDraw.isRepeatable'), align: 'center', key: 'isRepeatable', render(row) {
    return row.isRepeatable ? $t('common.yes') : $t('common.no')
  } },
  {
    title: $t('luckyDraw.eligibleGroups'),
    align: 'center',
    key: 'luckyDrawEligibleGroups',
    render(row) {
      const ticketTypeNames = row.luckyDrawEligibleGroups.map(group => group.ticketType.name)
      return ticketTypeNames.join(', ')
    },
  },
  {
    title: $t('common.action'),
    align: 'center',
    key: 'actions',
    render(row) {
      return (
        <NSpace justify="center">
          <NTooltip>
            {{
              default: () => $t('common.setting'),
              trigger: () => (
                <NButton size="small" onClick={() => handlePanel(row)}>
                  <icon-park-outline-setting-two />
                </NButton>
              ),
            }}
          </NTooltip>

          <NTooltip>
            {{
              default: () => $t('common.edit'),
              trigger: () => (
                <NButton size="small" onClick={() => handleEdit(row)}>
                  <icon-park-outline-edit-two />
                </NButton>
              ),
            }}
          </NTooltip>

          <NTooltip>
            {{
              default: () => $t('common.delete'),
              trigger: () => (
                <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
                  {{
                    default: () => $t('common.confirmDelete'),
                    trigger: () => (
                      <NButton size="small" type="error" ghost>
                        <icon-park-outline-delete />
                      </NButton>
                    ),
                  }}
                </NPopconfirm>
              ),
            }}
          </NTooltip>
        </NSpace>
      )
    },
  },
]

const route = useRoute()

onMounted(async () => {
  ticketStore.eventDetailId = Number(route.query.eventDetailId)
  luckyDrawStore.eventDetailId = Number(route.query.eventDetailId)
  ticketStore.eventId = Number(route.query.eventId)
  await fetchLuckyDrawList()
})
</script>

<template>
  <NSpace vertical size="large" class="flex-1">
    <n-card>
      <NSpace vertical size="large">
        <div class="flex gap-4">
          <NButton type="primary" @click="handleAddTable">
            <template #icon>
              <icon-park-outline-add-one />
            </template>
            {{ $t('common.addNew') }}
          </NButton>
          <n-input-group class="min-w-80 w-auto">
            <n-input v-model:value="luckyDrawStore.searchKeyword" :placeholder="`${$t('common.search')} Name Or Description`" clearable />
            <NButton type="primary" @click="fetchLuckyDrawList(true)">
              <icon-park-outline-search />
            </NButton>
          </n-input-group>
          <NButton strong ghost class="ml-a" @click="openSearchModal">
            <template #icon>
              <icon-park-outline-filter />
            </template>
            {{ $t('common.filter') }}
          </NButton>
          <!-- <NButton strong secondary>
            <template #icon>
              <icon-park-outline-download />
            </template>
            {{ $t('common.export') }}
          </NButton> -->
        </div>
        <div style="white-space: pre;" class="mt-2">
          <n-data-table :columns="columns" :data="luckyDrawStore.luckyDrawList" :loading="loading" />
        </div>
        <Pagination
          :count="luckyDrawStore.totalItems"
          :page="luckyDrawStore.currentPage"
          :page-size="luckyDrawStore.currentSize"
          @change="luckyDrawStore.handlePageChange"
        />
        <TableModal
          v-model:visible="visible"
          :type="modalType"
          :modal-data="luckyDrawStore.editData"
          :on-create="handleCreate"
          :on-update="handleUpdate"
        />
        <SearchModal
          v-model:visible="searchModalVisible"
          :type="modalType"
          :on-search="handleCriteriaFilter"
        />
        <PanelModal
          v-model:visible="panelModalVisible"
          :modal-data="selectedLuckyDraw"
        />
      </NSpace>
    </n-card>
  </NSpace>
</template>
