<script setup>
import { onMounted, ref } from 'vue'

const rawHtml = ref('')
const drawResult = ref('')
const countdown = ref(0)

onMounted(() => {
  window.addEventListener('message', (event) => {
    if (event.data?.target === 'metamask-inpage' || event.data?.name === 'metamask-provider') {
      return
    }

    if (event.data === 'resetCountdown') {
      resetCountdown()
    }
    else {
      drawResult.value = event.data
      startCountdown()
    }
  })
})

let countdownInterval = null

function resetCountdown() {
  clearInterval(countdownInterval)
  countdown.value = 5
  updateContent(countdown.value)
}

function startCountdown() {
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }

  countdown.value = 5
  updateContent(countdown.value)

  countdownInterval = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value -= 1
      updateContent(countdown.value)
    }
    else {
      clearInterval(countdownInterval)
      updateContent(drawResult.value)
    }
  }, 1000)
}

onMounted(async () => {
  // fetch
  const htmlFromDB = `<style scoped>
      .container {
          padding: 20px;
      }

      .draw-wrapper {
        background: url('https://ems-public-icons.s3.ap-southeast-1.amazonaws.com/luckydraw/background.png');
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat
      }

      .draw-wrapper p {
        font-size: 2rem;
        font-weight: bold;
      }

      .n-card {
        margin: 20px 0;
      }

      .slot {
        border:14px dotted #fff;
        border-radius:30px;
        margin:auto;
        margin-top:3rem;
        margin-bottom:10rem;
      }

      .slot-inner{
        padding:3rem;
        text-align: center;;
        background:#fff;
        min-height:150px;
        border-radius:20px;
        display: flex;
        margin:auto;
        justify-content: center;
        color:#000;
        box-shadow: 1px 0px 110px 4px rgba(255,255,255,0.75);
        -webkit-box-shadow: 1px 0px 110px 4px rgba(255,255,255,0.75);
        -moz-box-shadow: 1px 0px 110px 4px rgba(255,255,255,0.75);
      }
      </style>
      <div class="draw-wrapper min-h-screen px-4">
        <div class="flex justify-between md:flex-row flex-col gap-4xl sm:mb-0 mb-12">
          <img src="https://ems-public-icons.s3.ap-southeast-1.amazonaws.com/luckydraw/gala_tp_left.png" alt="" class="logo-top-l">
        </div>
        <img src="https://ems-public-icons.s3.ap-southeast-1.amazonaws.com/luckydraw/luckydraw-words.png" alt="" class="w-160 mx-auto z-10">
        <div class="slot px-4 py-3 w-80vw sm:min-w-160 sm:w-50vw">
          <div class="slot-inner">
            <p id="countdown" class="text-5xl">
            </p>
          </div>
        </div>
      </div>
  `

  const { html, css } = extractStyleAndHtml(htmlFromDB)

  rawHtml.value = html
  injectStyle(css)
})

function updateContent(content) {
  const el = document.getElementById('countdown')

  if (!el)
    return

  if (typeof content === 'object') {
    if (Array.isArray(content)) {
      el.innerHTML = content
        .map(item =>
          `${item.ticketNumber}<br>`
          + `${item.holderName}`,
        )
        .join('<br><br>')
    }
  }
  else {
    el.textContent = content
  }
}

// Utility to split <style> and actual HTML
function extractStyleAndHtml(htmlString) {
  const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/i
  const styleMatch = htmlString.match(styleRegex)
  const css = styleMatch ? styleMatch[1] : ''
  const html = htmlString.replace(styleRegex, '')
  return { html, css }
}

// Inject CSS into document head
function injectStyle(css) {
  if (!css)
    return
  const styleEl = document.createElement('style')
  styleEl.textContent = css
  document.head.appendChild(styleEl)
}
</script>

<template>
  <div v-html="rawHtml" />
</template>
