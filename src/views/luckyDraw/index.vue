<script setup lang="tsx">
import { NSpace } from 'naive-ui'
import TableModal from './components/TableModal.vue'
import Modal from './components/Modal.vue'
import { useBoolean } from '@/hooks'
import { useLuckyDrawStore } from '@/store/lucky-draw'
import { useTicketStore } from '@/store/ticket'
import SourClick from '@/assets/sour-click.svg'
import Lime from '@/assets/lime.png'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const { bool: visible, setTrue: openModal } = useBoolean(false)
const { bool: addModalVisible, setTrue: openAddModal } = useBoolean(false)

const luckyDrawStore = useLuckyDrawStore()
const ticketStore = useTicketStore()

const modalType = ref<ModalType>('add')

function setModalType(type: ModalType) {
  modalType.value = type
}

function handleEdit(row: Entity.LuckyDraw) {
  luckyDrawStore.editData = row
  setModalType('edit')
  openModal()
}

function handleAddTable() {
  setModalType('add')
  openAddModal()
}

async function fetchLuckyDrawList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await luckyDrawStore.fetchLuckyDrawList(reset)
  window.$loadingBar.finish()
  endLoading()
}

async function handleCreate(data: Entity.LuckyDraw.CreateParams) {
  await luckyDrawStore.createLuckyDraw(data)
  luckyDrawStore.handleResetSearch()
  fetchLuckyDrawList(true)
}

async function handleUpdate(data: Entity.LuckyDraw.UpdateParams) {
  await luckyDrawStore.updateLuckyDraw(data)
  luckyDrawStore.handleResetSearch()
  fetchLuckyDrawList(true)
}

const route = useRoute()

onMounted(async () => {
  ticketStore.eventDetailId = Number(route.query.eventDetailId)
  luckyDrawStore.eventDetailId = Number(route.query.eventDetailId)
  ticketStore.eventId = Number(route.query.eventId)
  await fetchLuckyDrawList()
})
</script>

<template>
  <NSpace vertical size="large" class="flex-1">
    <n-grid cols="24" responsive="screen" x-gap="16" y-gap="16" style="gap:30px !important;">
      <template v-if="loading">
        <n-grid-item v-for="i in [0, 1]" :key="i" span="8">
          <n-card class="shadow-md rounded-theme w-full py-1 relative h-300px hover:cursor-pointer">
            <n-skeleton :repeat="10" class="w-100% mb-3" />
          </n-card>
        </n-grid-item>
      </template>

      <template v-else>
        <template v-if="luckyDrawStore.luckyDrawList.length > 0">
          <n-grid-item v-for="item in luckyDrawStore.luckyDrawList" :key="item.id" span="8">
            <n-card class="shadow-md rounded-theme w-full py-1 relative min-h-300px hover:cursor-pointer" @click="handleEdit(item)">
              <n-image :src="Lime" class="absolute top-[0px] left-[-15px] w-30px object-cover" preview-disabled />
              <n-flex vertical>
                <n-flex vertical>
                  <div class="fw-semibold text-lg">
                    {{ item.name }}
                  </div>
                  <div class="text-muted">
                    <n-ellipsis>{{ item.description }}</n-ellipsis>
                  </div>
                </n-flex>
                <n-divider style="margin:0;" />
                <n-flex vertical>
                  <div class="text-lg">
                    {{ item.winnerQuantity }} Winner(s)
                  </div>
                  <div class="text-lg">
                    {{ item.isRepeatable ? 'Repeatable' : 'No Repeatable' }}
                  </div>
                  <div class="flex flex-col">
                    <span class="text-lg">Elligible Groups</span>
                    <div class="flex flex-wrap mt-4 gap-2">
                      <div v-for="group in item.luckyDrawEligibleGroups" :key="group.ticketType.id" class="rounded-full bg-white shadow px-5 py-2">
                        <span class="text-[#448469] fw-bold">{{ group.ticketType.name }}</span>
                      </div>
                    </div>
                  </div>
                </n-flex>
              </n-flex>
            </n-card>
          </n-grid-item>
        </template>
        <n-grid-item span="8">
          <n-card class="rounded-theme shadow-md h-100% min-h-300px cursor-pointer hover:filter-drop-shadow hover:brightness-95 transition bg-[#fff] relative" content-class="flex items-center justify-center" @click="handleAddTable">
            <n-image :src="Lime" class="absolute top-[0px] left-[-15px] w-30px object-cover" preview-disabled />
            <n-flex vertical align="center" class="text-center">
              <n-flex class="mx-auto" align="center">
                <img :src="SourClick" alt="" class="h-[60px] opacity-[0.7] object-contain">
              </n-flex>
              <h2 class="text-gray-800 font-bold text-xl mt-8 mb-2 text-success opacity-[0.7]">
                BUILD AN EXPERIENCE WITH US
              </h2>
            </n-flex>
          </n-card>
        </n-grid-item>
      </template>
    </n-grid>

    <Modal v-model:visible="addModalVisible" :on-create="handleCreate" />

    <TableModal
      v-model:visible="visible"
      :type="modalType"
      :modal-data="luckyDrawStore.editData"
      :on-create="handleCreate"
      :on-update="handleUpdate"
    />
  </NSpace>
</template>
