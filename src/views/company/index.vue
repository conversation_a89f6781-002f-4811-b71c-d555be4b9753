<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui'
import { NButton, NSpace, NTooltip } from 'naive-ui'
import { RouterLink } from 'vue-router'
// import TableModal from './components/TableModal.vue'
import SearchModal from './components/SearchModal.vue'
import { useCompanyStore } from '@/store/company'
import { useBoolean } from '@/hooks'
import { useCountryStore } from '@/store/country'
import { $t } from '@/utils/i18n'
// import { defaultCriteriaValue } from '@/utils/default-values'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
// const { bool: visible, setTrue: openModal } = useBoolean(false)
// const { bool: searchModalVisible, setTrue: openSearchModal, setFalse: closeSearchModal } = useBoolean(false)

const countryStore = useCountryStore()
const companyStore = useCompanyStore()

// const modalType = ref<ModalType>('add')

// function setModalType(type: ModalType) {
//   modalType.value = type
// }

// function handleEdit(row: Entity.Company) {
//   companyStore.editData = row
//   setModalType('edit')
//   openModal()
// }

// function handleAddTable() {
//   setModalType('add')
//   openModal()
// }

async function fetchCompanyList(reset: boolean = false) {
  startLoading()
  window.$loadingBar.start()
  await companyStore.fetchCompanyList(reset)
  window.$loadingBar.finish()
  endLoading()
}

// async function handleCreate(data: Entity.Company.CreateParams) {
//   await companyStore.createCompany(data)
//   companyStore.handleResetSearch()
//   fetchCompanyList(true)
// }

// async function handleUpdate(data: Entity.Company.UpdateParams) {
//   await companyStore.updateCompany(data)
//   companyStore.handleResetSearch()
//   fetchCompanyList(true)
// }

// async function handleDelete(id: number) {
//   await companyStore.deleteCompany(id)
//   companyStore.handleResetSearch()
//   fetchCompanyList(true)
// }

// async function handleCriteriaFilter() {
//   fetchCompanyList(false)
//   // closeSearchModal()
// }

const columns: DataTableColumns<Entity.Company> = [
  {
    title: '#',
    align: 'center',
    key: 'index',
    render(row, index) {
      return index + 1 + ((companyStore.currentPage - 1) * companyStore.currentSize)
    },
  },
  { title: $t('company.name'), align: 'center', key: 'name' },
  { title: $t('company.email'), align: 'center', key: 'email' },
  { title: $t('company.addressLine1'), align: 'center', key: 'addressLine1' },
  { title: $t('company.addressLine2'), align: 'center', key: 'addressLine2' },
  {
    title: `${$t('company.country')} ${$t('company.callingCode')}`,
    align: 'center',
    key: 'countryId',
    render(row) {
      return countryStore.getCountryById(row.countryId)?.name || 'Unknown'
    },
  },
  {
    title: $t('company.phone'),
    align: 'center',
    key: 'countryId',
    render(row) {
      return `${countryStore.getCountryById(row.countryId)?.callingCode.code || ''} ${row.phone}`
    },
  },
  {
    title: $t('common.action'),
    align: 'center',
    key: 'actions',
    render(row) {
      return (
        <NSpace justify="center">
          <NTooltip>
            {{
              default: () => $t('route.event'),
              trigger: () => (
                <RouterLink
                  to={{
                    path: 'events',
                    query: { companyId: row.id },
                  }}
                >
                  <NButton size="small">
                    <icon-park-outline-calendar />
                  </NButton>
                </RouterLink>
              ),
            }}
          </NTooltip>

          {/* <NTooltip>
            {{
              default: () => $t('common.edit'),
              trigger: () => (
                <NButton size="small" onClick={() => handleEdit(row)}>
                  <icon-park-outline-edit-two />
                </NButton>
              ),
            }}
          </NTooltip> */}

          {/* <NTooltip>
            {{
              default: () => $t('common.delete'),
              trigger: () => (
                <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
                  {{
                    default: () => $t('common.confirmDelete'),
                    trigger: () => (
                      <NButton size="small" type="error" ghost>
                        <icon-park-outline-delete />
                      </NButton>
                    ),
                  }}
                </NPopconfirm>
              ),
            }}
          </NTooltip> */}
        </NSpace>
      )
    },
  },
]

onMounted(async () => {
  companyStore.searchKeyword = null

  await countryStore.countryList()
  await fetchCompanyList()
})
</script>

<template>
  <NSpace vertical size="large">
    <n-card>
      <NSpace vertical size="large">
        <div class="flex gap-4">
          <!-- <NButton type="primary" @click="handleAddTable">
            <template #icon>
              <icon-park-outline-add-one />
            </template>
            {{ $t('common.addNew') }}
          </NButton> -->
          <n-input-group class="min-w-80 w-auto">
            <n-input v-model:value="companyStore.searchKeyword" :placeholder="`${$t('common.search')} Name Or Email`" clearable />
            <NButton type="primary" @click="fetchCompanyList(true)">
              <icon-park-outline-search />
            </NButton>
          </n-input-group>
          <!-- <NButton strong ghost class="ml-a" @click="openSearchModal">
            <template #icon>
              <icon-park-outline-filter />
            </template>
            {{ $t('common.filter') }}
          </NButton> -->
        </div>
        <div style="white-space: pre;" class="mt-2">
          <n-data-table :columns="columns" :data="companyStore.companyList" :loading="loading" />
        </div>
        <Pagination
          :count="companyStore.totalItems"
          :page="companyStore.currentPage"
          :page-size="companyStore.currentSize"
          @change="companyStore.handlePageChange"
        />
        <!-- <TableModal
          v-model:visible="visible"
          :type="modalType"
          :modal-data="companyStore.editData"
          :on-create="handleCreate"
          :on-update="handleUpdate"
        /> -->
        <!-- <SearchModal
          v-model:visible="searchModalVisible"
          :type="modalType"
          :on-search="handleCriteriaFilter"
        /> -->
      </NSpace>
    </n-card>
  </NSpace>
</template>
