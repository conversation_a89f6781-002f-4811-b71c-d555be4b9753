<script setup lang="ts">
import { local } from '@/utils'
import { useBoolean } from '@/hooks'
import SwitchCompany from '@/layouts/components/modal/SwitchCompany.vue'

const { bool: modalVisible, setTrue: openModal, setFalse: closeModal } = useBoolean(false)

const userInfo = ref()

onBeforeMount(() => {
  userInfo.value = local.get('userInfo')
})

onMounted(async () => {
  openModal()
})
</script>

<template>
  <div>
    <Watermark :show-watermark="true" text="SOUR ems" />
    <SwitchCompany :visible="modalVisible" :close-modal="closeModal" :allow-cancel="false" :allow-logout="true" />
  </div>
</template>

<style scoped></style>
