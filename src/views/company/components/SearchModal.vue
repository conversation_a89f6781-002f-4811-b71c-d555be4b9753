<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue'
import type { SelectOption } from 'naive-ui'
import { $t } from '@/utils/i18n'
import { useCriteriaStore } from '@/store/criteria'
import { useCompanyStore } from '@/store/company'

// Interface declaration
interface Props {
  visible: boolean
  modalData?: Entity.Ticket | null
  onSearch: () => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// End of Type and Interface declaration

const columnOptions: SelectOption[] = [
  { label: 'Name', value: 'name' },
  { label: 'Email', value: 'email' },
  { label: 'City', value: 'city' },
  { label: 'State', value: 'state' },
  { label: 'Postal Code', value: 'postalCode' },
  { label: 'Country', value: 'country' },
  { label: 'Phone', value: 'phone' },
]

const companyStore = useCompanyStore()
const criteriaStore = useCriteriaStore(companyStore.searchModel)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    preset="card"
    title="Advanced Filter"
    class="w-700px min-h-400px"
    :segmented="{ content: true, action: true }"
  >
    <n-radio-group v-model:value="companyStore.matchType" name="matchType">
      <n-radio value="all">
        {{ $t('common.allOfTheFollowing') }}
      </n-radio>
      <n-radio value="any">
        {{ $t('common.anyOfTheFollowing') }}
      </n-radio>
    </n-radio-group>

    <div>
      <div v-for="(criterion, index) in companyStore.searchModel" :key="index">
        <div class="flex flex-col sm:flex-row gap-x-2 gap-y-2 sm:gap-y-0 mt-4">
          <n-select v-model:value="criterion.column" placeholder="Select Column" :options="columnOptions" />
          <n-select v-model:value="criterion.type" placeholder="Select Type" :options="criteriaStore.criteriaOptions" />
          <n-input v-model:value="criterion.value" placeholder="Value" />
          <n-button type="error" secondary @click="criteriaStore.removeCriteria(index)">
            <icon-park-outline-delete />
          </n-button>
        </div>
      </div>
      <n-button class="mt-4" type="primary" secondary @click="criteriaStore.addCriteria">
        Add Criteria
      </n-button>
    </div>

    <template #action>
      <n-flex justify="left">
        <n-button @click="modalVisible = false">
          Cancel
        </n-button>
        <n-button type="primary" secondary @click="onSearch">
          Apply
        </n-button>
      </n-flex>
    </template>
  </n-modal>
</template>

<style scoped></style>
