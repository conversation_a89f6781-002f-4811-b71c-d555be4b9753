<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import { useCompanyStore } from '@/store/company'
import { useCountryStore } from '@/store/country'
import { $t } from '@/utils/i18n'

// Interfarce declaration
interface Props {
  visible: boolean
  type?: ModalType
  modalData?: Entity.Company | null
  onCreate?: (data: Entity.Company.CreateParams) => Promise<void>
  onUpdate?: (data: Entity.Company.UpdateParams) => Promise<void>
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})
// End of Type and Interface declaration

const emit = defineEmits<Emits>()

const companyStore = useCompanyStore()
const countryStore = useCountryStore()

const formModel = ref<Nullable<Entity.Company.CreateParams>>(companyStore.defaultFormModel)

const modalVisible = computed({
  get: () => props.visible,
  set: visible => emit('update:visible', visible),
})

const title = computed(() => ({
  add: $t('common.add'),
  edit: $t('common.edit'),
}[props.type || 'add']))

function updateFormModelByModalType() {
  if (props.type === 'edit' && props.modalData) {
    formModel.value = { ...props.modalData }
  }
  else {
    formModel.value = {
      name: null,
      email: null,
      addressLine1: null,
      addressLine2: null,
      city: null,
      state: null,
      postalCode: null,
      countryId: null,
      callingCodeId: null,
      phone: null,
    }
  }
}

function onCountryChange(newCountryId: number) {
  const country = countryStore.getCountryById(newCountryId)
  formModel.value.callingCodeId = country !== null ? country.callingCode.id : 0
}

async function handleSubmit() {
  if (props.type === 'add' && props.onCreate) {
    await props.onCreate(formModel.value as Entity.Company.CreateParams)
  }
  else if (props.type === 'edit' && props.onUpdate && props.modalData) {
    await props.onUpdate(formModel.value as Entity.Company.UpdateParams)
  }

  modalVisible.value = false
}

watch(() => props.visible, (newValue) => {
  if (newValue)
    updateFormModelByModalType()
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    preset="card"
    :title="title"
    class="w-700px"
    :segmented="{ content: true, action: true }"
  >
    <n-form label-placement="top" :model="formModel" label-align="left">
      <n-grid cols="1 s:2" x-gap="18" responsive="screen">
        <n-form-item-grid-item :label="$t('company.name')" path="name">
          <n-input v-model:value="formModel.name" />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.email')" path="email">
          <n-input v-model:value="formModel.email" />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.addressLine1')" path="addressLine1">
          <n-input v-model:value="formModel.addressLine1" />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.addressLine2')" path="addressLine2">
          <n-input v-model:value="formModel.addressLine2" />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.city')" path="city">
          <n-input v-model:value="formModel.city" />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.state')" path="state">
          <n-input v-model:value="formModel.state" />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.postalCode')" path="postalCode">
          <n-input-number v-model:value="formModel.postalCode" class="w-full" :show-button="false" />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.country')" path="countryId">
          <n-select
            v-model:value="formModel.countryId"
            filterable
            :placeholder="$t('common.select')"
            :options="countryStore.countryOptions"
            @update:value="onCountryChange"
          />
        </n-form-item-grid-item>

        <n-form-item-grid-item :label="$t('company.phone')" path="phone">
          <n-input v-model:value="formModel.phone" />
        </n-form-item-grid-item>
      </n-grid>
    </n-form>
    <template #action>
      <n-space justify="center">
        <n-button @click="modalVisible = false">
          Cancel
        </n-button>
        <n-button type="primary" @click="handleSubmit">
          Submit
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<style scoped></style>
