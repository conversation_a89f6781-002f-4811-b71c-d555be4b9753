import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { local } from '@/utils'

const axiosInstance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add a request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    const token = local.get('idToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

const api = {
  async get<T>(url: string, config: AxiosRequestConfig = {}, count = 0): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.get(url, config)
      handleResponse(response)
      return response.data
    }
    catch (error) {
      while (count < 3 && error.response && (error.response.error === 'Connection terminated unexpectedly' || error.response.message === 'Connection terminated unexpectedly')) {
        count++
        await recast<T>(url, config, count)
      }

      handleError(error)
      throw error
    }
  },

  async post<T>(url: string, data: any, config: AxiosRequestConfig = {}): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.post(url, data, config)
      handleResponse(response)
      return response.data
    }
    catch (error) {
      handleError(error)
      throw error
    }
  },

  async put<T>(url: string, data: any, config: AxiosRequestConfig = {}): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.put(url, data, config)
      handleResponse(response)
      return response.data
    }
    catch (error) {
      handleError(error)
      throw error
    }
  },

  async delete<T>(url: string, config: AxiosRequestConfig = {}): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.delete(url, config)
      handleResponse(response)
      return response.data
    }
    catch (error) {
      handleError(error)
      throw error
    }
  },
}

function handleResponse(response: AxiosResponse) {
  if (response.data.success) {
    // window.$message.success(response.data.message || 'Request successful')
  }
  else {
    window.$message.error(response.data.message || 'Request failed')
  }
}

async function handleError(error: any) {
  if (error.response && error.response.status === 422) {
    const errors = error.response.data?.error || []
    errors.forEach((err: string) => {
      window.$message.error(err)
    })
  }
  else if (error.response.data.error && error.response.data.error.startsWith('Token expired')) {
    window.$message.error('Session has expired, please re-login')
    handleLogout()
  }
  else if (error.response.data.error && error.response.data.error.startsWith('Token is missing')) {
    window.$message.error('Unauthorized user')
    handleLogout()
  }
  else if (error.response.status === 401) {
    window.$message.error('Unauthorized user')
    handleLogout()
    window.location.reload()
  }
  else {
    window.$message.error(error.response?.data?.error || error.response?.data?.message || 'An error occurred')
  }
}

async function recast<T>(url, config, count) {
  await api.get<T>(url, config, count)
}

function handleLogout() {
  try {
    const pinia = getActivePinia()
    if (pinia) {
      pinia._s.forEach((store) => {
        if (store.reset) {
          store.reset()
        }
        else if (store.$reset) {
          store.$reset()
        }
        else {
          console.warn(`Store ${store.$id} does not have a reset method`)
        }
      })
    }
    local.remove('idToken')
    local.remove('accessToken')
    local.remove('refreshToken')
    local.remove('userInfo')
    localStorage.removeItem('companyId')
    window.location.reload()
  }
  catch (e) {
    console.log(e)
  }
}

export default api
