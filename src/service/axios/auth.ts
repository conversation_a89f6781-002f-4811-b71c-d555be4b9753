import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { local } from '@/utils'

const axiosInstance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_AUTH_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

const api = {
  async get<T>(url: string, config: AxiosRequestConfig = {}, auth: boolean = false): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.get(url, getConfig(config, auth))
      handleResponse(response)
      return response.data
    }
    catch (error) {
      handleError(error)
      throw error
    }
  },

  async post<T>(url: string, data: any, config: AxiosRequestConfig = {}, auth: boolean = false): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.post(url, data, getConfig(config, auth))
      handleResponse(response)
      return response.data
    }
    catch (error) {
      handleError(error)
      throw error
    }
  },

  async put<T>(url: string, data: any, config: AxiosRequestConfig = {}, auth: boolean = false): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.put(url, data, getConfig(config, auth))
      handleResponse(response)
      return response.data
    }
    catch (error) {
      handleError(error)
      throw error
    }
  },

  async delete<T>(url: string, config: AxiosRequestConfig = {}, auth: boolean = false): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.delete(url, getConfig(config, auth))
      handleResponse(response)
      return response.data
    }
    catch (error) {
      handleError(error)
      throw error
    }
  },
}

function getConfig(config: AxiosRequestConfig, auth: boolean): AxiosRequestConfig {
  if (auth) {
    const token = local.get('idToken')
    return {
      ...config,
      headers: {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      },
    }
  }
  return config
}

function handleResponse(response: AxiosResponse) {
  if (response.data.success) {
    // window.$message.success(response.data.message || 'Request successful')
  }
  else {
    window.$message.error(response.data.message || 'Request failed')
  }
}

function handleError(error: any) {
  if (error.response && error.response.status === 422) {
    const errors = error.response.data?.error || []
    errors.forEach((err: string) => {
      window.$message.error(err)
    })
  }
  else {
    window.$message.error(error.response?.data?.error || error.response?.data?.message || 'An error occurred')
  }
}

export default api
