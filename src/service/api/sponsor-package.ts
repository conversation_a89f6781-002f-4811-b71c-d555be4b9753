import api from '../axios'

export function list<T>(eventId: number, params: T): Promise<ApiResponse<ListResponse<Entity.SponsorPackage>>> {
  return api.get('/sponsor/packages', {
    params: {
      eventId,
      ...params,
    },
  })
}

export function create<T>(params: T): Promise<ApiResponse<Entity.SponsorPackage>> {
  return api.post('/sponsor/packages', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/sponsor/packages/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/sponsor/packages/${id}`)
}
