import api from '../axios'

export function useEmailCampaignApi() {
  function list<T>(eventId, params: T): Promise<ApiResponse<TicketListResponse<Entity.EmailCampaign>>> {
    const { sort, ...rest }: any = params

    const queryParams: Record<string, any> = {
      eventId,
      ...rest,
    }

    if (Array.isArray(sort)) {
      sort.forEach((s) => {
        queryParams[`sort[${s.column}]`] = s.order
      })
    }

    return api.get('/email/campaigns', {
      params: queryParams,
    })
  }

  function get(id: number): Promise<ApiResponse<ListResponse<Entity.EmailCampaign>>> {
    return api.get(`/email/campaigns/${id}`)
  }

  function create<T>(params: T): Promise<ApiResponse<Entity.EmailCampaign>> {
    return api.post('/email/campaigns', params)
  }

  function update<T>(id: number, params: T): Promise<ApiResponse<boolean>> {
    return api.put(`/email/campaigns/${id}`, params)
  }

  function del(id: number): Promise<ApiResponse<null>> {
    return api.delete(`/email/campaigns/${id}`)
  }

  function sendEmail(params: Entity.EmailCampaign.SendEmailParams): Promise<ApiResponse<null>> {
    return api.post(`/email/campaigns/send-email`, params)
  }

  return {
    list,
    get,
    create,
    update,
    del,
    sendEmail,
  }
}
