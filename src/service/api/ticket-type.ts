import api from '../axios'

export function list<T>(params: T): Promise<ApiResponse<ListResponse<Entity.TicketType>>> {
  return api.get('/ticket/types', { params })
}

export function get(id: number): Promise<ApiResponse<ListResponse<Entity.TicketType>>> {
  return api.get(`/ticket/types/${id}`)
}

export function create<T>(params: T): Promise<ApiResponse<Entity.TicketType>> {
  return api.post('/ticket/types', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/ticket/types/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/ticket/types/${id}`)
}

export function assignment<T>(params: T): Promise<ApiResponse<Entity.TicketType.Assignment[]>> {
  return api.get('/ticket/types-assignment', {
    params: {
      ...params,
    },
  })
}
