import api from '../axios'

export function list(eventId: number): Promise<ApiResponse<ListResponse<Entity.EventDetail>>> {
  return api.get('/event/details', {
    params: {
      eventId,
    },
  })
}

export function get(id: number): Promise<ApiResponse<Entity.EventDetail>> {
  return api.get(`/event/details/${id}`)
}

export function create<T>(params: T): Promise<ApiResponse<Entity.EventDetail>> {
  return api.post('/event/details', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/event/details/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/event/details/${id}`)
}

export function saveHallLayout<T>(params: T): Promise<ApiResponse<null>> {
  return api.post(`/event/details/hall-layout`, params)
}

export function postPicture<T>(id: number, params: T): Promise<ApiResponse<PictureUrl>> {
  const header = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }
  return api.post(`/event/details/${id}/picture`, params, header)
}
