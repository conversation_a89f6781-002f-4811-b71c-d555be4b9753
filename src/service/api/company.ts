import api from '../axios'

export function list<T>(params: T): Promise<ApiResponse<ListResponse<Entity.Company>>> {
  return api.get('/companies', { params })
}

export function get(id: number): Promise<ApiResponse<ListResponse<Entity.Company>>> {
  return api.get(`/companies/${id}`)
}

export function create<T>(params: T): Promise<ApiResponse<Entity.Company>> {
  return api.post('/companies', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/companies/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/companies/${id}`)
}
