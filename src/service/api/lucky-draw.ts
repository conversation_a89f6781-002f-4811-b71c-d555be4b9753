import api from '../axios'

export function list<T>(params: T): Promise<ApiResponse<ListResponse<Entity.LuckyDraw>>> {
  return api.get('/lucky-draws', { params })
}

export function get(id: number): Promise<ApiResponse<ListResponse<Entity.LuckyDraw>>> {
  return api.get(`/lucky-draws/${id}`)
}

export function create<T>(params: T): Promise<ApiResponse<Entity.LuckyDraw>> {
  return api.post('/lucky-draws', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/lucky-draws/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/lucky-draws/${id}`)
}

export function winners<T>(params: T): Promise<ApiResponse<ListResponse<Entity.LuckyDraw.Winner>>> {
  return api.get('/lucky-draw/winners', { params })
}

export function getPossibleLuckyDrawWinners<T>(params: T): Promise<ApiResponse<ListResponse<Entity.Ticket>>> {
  return api.get('/lucky-draws/possible/winners', { params })
}
