import api from '../axios'

export function list<T>(eventId, params: T): Promise<ApiResponse<ListResponse<Entity.Sponsor>>> {
  return api.get('/sponsors', {
    params: {
      eventId,
      ...params,
    },
  })
}

export function sponsorDashboard(eventId: number): Promise<ApiResponse<Entity.Sponsor.VendorDashboard>> {
  return api.get('/dashboard/sponsor', {
    params: {
      eventId,
    },
  })
}

export function create<T>(params: T): Promise<ApiResponse<Entity.Sponsor>> {
  return api.post('/sponsors', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/sponsors/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/sponsors/${id}`)
}

export function upload<T>(params: T): Promise<ApiResponse<string>> {
  return api.post('/sponsors/upload-request', params)
}

export function files(id: number): Promise<ApiResponse<ListResponse<File>>> {
  return api.get(`/sponsors/${id}/files`)
}

export function delFile<T>(id: number, params: T): Promise<ApiResponse<string>> {
  return api.delete(`sponsors/${id}/files`, { params: { ...params } })
}

export function exports<T>(params: T): Promise<ApiResponse<ExportFile>> {
  return api.post('/sponsors/export', params)
}