import moment from 'moment-timezone'
import api from '../axios'
import publicApi from '../axios/public'

export function list<T>(eventId, params: T, tableAssign: string): Promise<ApiResponse<TicketListResponse<Entity.Ticket>>> {
  const { sort, ...rest }: any = params

  const queryParams: Record<string, any> = {
    eventId,
    tableAssign,
    ...rest,
  }

  if (Array.isArray(sort)) {
    sort.forEach((s) => {
      queryParams[`sort[${s.column}]`] = s.order
    })
  }

  return api.get('/tickets', {
    params: queryParams,
  })
}

export function listNoPagination<T>(eventId, params: T, tableAssign: string): Promise<ApiResponse<ListResponse<Entity.Ticket>>> {
  return api.get('/tickets/no-pagination', { params: {
    eventId,
    ...params,
    tableAssign,
  } })
}

export function get(id: number): Promise<ApiResponse<ListResponse<Entity.Ticket>>> {
  return api.get(`/tickets/${id}`)
}

export function create<T>(params: T): Promise<ApiResponse<Entity.Ticket>> {
  return api.post('/tickets', params)
}

export function guest<T>(params: T): Promise<ApiResponse<Entity.Ticket>> {
  return api.post('/tickets/guest', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/tickets/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/tickets/${id}`)
}

export function preview(ticketNumber: string): Promise<ApiResponse<Preview>> {
  return api.get(`/tickets/${ticketNumber}/preview`, {
    params: { timezone: moment.tz.guess() },
  })
}

export function publicPreview(ticketNumber: string): Promise<ApiResponse<string>> {
  return publicApi.get(`/tickets/${ticketNumber}/preview`, {
    params: { timezone: moment.tz.guess() },
  })
}

export function email<T>(params: T): Promise<ApiResponse<ListResponse<null>>> {
  return api.post('/email/campaigns/send-email', params)
}

export function checkInTickets<T>(eventId: number, eventDetailId: number, params: T): Promise<ApiResponse<ListResponse<Entity.Ticket>>> {
  return api.get('/ticket/check-ins-tickets', {
    params: {
      eventId,
      eventDetailId,
      isCheckedIn: false,
      ...params,
    },
  })
}

export function imports(eventId: number, file: File): Promise<ApiResponse<null>> {
  const formData = new FormData()

  formData.append('eventId', eventId.toString())
  formData.append('file', file)

  return api.post('/tickets/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

export function exports<T>(params: T): Promise<ApiResponse<ExportFile>> {
  return api.post('/tickets/export', params)
}
