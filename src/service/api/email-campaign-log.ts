import api from '../axios'

export function useEmailCampaignLogApi() {
  function list(params: Entity.EmailCampaignLog.ListParams): Promise<ApiResponse<TicketListResponse<Entity.EmailCampaignLog>>> {
    return api.get('/email/campaign/logs', { params })
  }

  function download(params: Entity.EmailCampaignLog.DownloadParams): Promise<ApiResponse<Entity.EmailCampaignLog.DownloadLink>> {
    return api.post(`/email/campaign/logs/download`, params)
  }

  return {
    list,
    download,
  }
}
