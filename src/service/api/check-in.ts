import api from '../axios'

export function list<T>(params: T, eventDetailId: number): Promise<ApiResponse<ListResponse<Entity.CheckIn>>> {
  return api.get('/ticket/check-ins', {
    params: {
      ...params,
      eventDetailId,
    },
  })
}

export function get(id: number): Promise<ApiResponse<ListResponse<Entity.CheckIn>>> {
  return api.get(`/ticket/check-ins/${id}`)
}

export function create<T>(params: T): Promise<ApiResponse<Entity.Ticket>> {
  return api.post('/ticket/check-ins', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/ticket/check-ins/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/ticket/check-ins/${id}`)
}

export function summary(eventId: number): Promise<ApiResponse<Entity.Ticket.CheckInSummary[]>> {
  return api.get('/ticket/check-ins-summary', {
    params: {
      eventId,
    },
  })
}

export function exports<T>(params: T): Promise<ApiResponse<ExportFile>> {
  return api.post('/ticket/check-ins/export', params)
}