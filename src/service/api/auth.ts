import { request } from '../http'
import api from '../axios/auth'

export function fetchLogin(data: <PERSON>og<PERSON>) {
  const methodInstance = api.post<Api.Login.Info>('/login', data)
  return methodInstance
}
export function fetchUpdateToken(data: any) {
  const method = request.Post<Service.ResponseResult<Api.Login.Info>>('/updateToken', data)
  method.meta = {
    authRole: 'refreshToken',
  }
  return method
}

export function fetchUserRoutes(params: { id: number }) {
  return request.Get<Service.ResponseResult<AppRoute.RowRoute[]> >('/getUserRoutes', { params })
}

export function changePassword(data: IChangePassword): Promise<ApiResponse<any>> {
  return api.post<Service.ResponseResult<boolean>>('/forgot-password', data, {}, true)
}
