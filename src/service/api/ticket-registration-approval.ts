import api from '../axios'

export function list<T>(eventId: number, params: T): Promise<ApiResponse<ListResponse<Entity.TicketRegistrationApproval>>> {
  const { sort, ...rest }: any = params

  const queryParams: Record<string, any> = {
    eventId,
    ...rest,
  }

  if (Array.isArray(sort)) {
    sort.forEach((s) => {
      queryParams[`sort[${s.column}]`] = s.order
    })
  }

  return api.get('/ticket/registrations', {
    params: queryParams,
  })
}

export function approve<T>(params: T): Promise<ApiResponse<null>> {
  return api.put(`/ticket/registrations/approve`, params)
}

export function reject<T>(params: T): Promise<ApiResponse<null>> {
  return api.put(`/ticket/registrations/reject`, params)
}

export function del<T>(params: T): Promise<ApiResponse<null>> {
  return api.delete(`/ticket/registrations`, {
    params: { id: params },
  })
}

export function exports<T>(params: T): Promise<ApiResponse<ExportFile>> {
  return api.post('/ticket/registrations/export', params)
}
