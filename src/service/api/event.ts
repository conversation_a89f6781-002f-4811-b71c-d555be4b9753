import api from '../axios'

export function list<T>(companyId: number, params?: T): Promise<ApiResponse<ListResponse<Entity.Event>>> {
  return api.get(`/events`, { params: { companyId, ...params } })
}

export function get(id: number): Promise<ApiResponse<ListResponse<Entity.Event>>> {
  return api.get(`/events/${id}`)
}

export function create<T>(params: T): Promise<ApiResponse<Entity.Event>> {
  return api.post('/events', params)
}

export function update<T>(id: number, params: T): Promise<ApiResponse<null>> {
  return api.put(`/events/${id}`, params)
}

export function del(id: number): Promise<ApiResponse<null>> {
  return api.delete(`/events/${id}`)
}
