/// <reference path="../global.d.ts"/>

namespace Entity {
  interface Ticket extends Timestamp {
    id: number
    eventId: number
    ticketNumber?: string
    ticketTypeId: number
    holderName: string
    companyName: string
    email: string
    contactNumber: string
    customNote: customKeyValue[]
    totalEmailSent?: number
    age: number
    gender: 'M' | 'F' | 'O'
    maritalStatusId: number
    ticketType?: Entity.TicketType
    tableSeat?: any
    qrCode?: string
    encryptedTicketNumber?: string
  }

  namespace Ticket {
      type SearchColumns = 'holderName' | 'email' | 'ticketTypeId' | 'companyName' | 'contactNumber' | 'totalEmailSent' | null

      interface SearchParams extends Partial<Nullable<Ticket>>, ListParam {}

      interface ListParams extends ListParam, SearchParams {}

      interface CreateParams extends Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'> {}
      interface CreateGuestParams extends Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'> {
        eventDetailId: number
      }

      interface UpdateParams extends Omit<Ticket, 'createdAt' | 'updatedAt'> {}

      interface Summary {
        ticketType: string
        totalTicket: number
        totalCheckIn: number
      }
      
      interface CheckInSummary {
        id: number
        name: string
        summary: Summary[]
      }
  }
}
