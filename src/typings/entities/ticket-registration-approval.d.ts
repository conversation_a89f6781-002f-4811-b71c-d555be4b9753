/// <reference path="../global.d.ts"/>

namespace Entity {
  interface ApprovalStatus {
    id: number
    name: string
  }

  interface TicketRegistrationApproval extends Timestamp {
    id: number
    eventId: number
    holderName: string
    companyName: string
    email: string
    contactNumber: string
    age: integer
    gender: string
    maritalStatusId: integer
    approvalStatusId: number
    approvalStatus?: ApprovalStatus
    customNote: customKeyValue[]
  }

  namespace TicketRegistrationApproval {
    type SearchColumns = 'holderName' | 'email' | 'ticketTypeId' | 'companyName' | 'contactNumber' | 'totalEmailSent' | null
    interface SearchParams extends Partial<Nullable<TicketRegistrationApproval>>, ListParam {}
    interface ListParams extends ListParam, SearchParams {}
  }
}
