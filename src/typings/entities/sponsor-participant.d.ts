/// <reference path="../global.d.ts"/>

namespace Entity {
  interface SponsorParticipant {
    id: number
    ticketId: number
    name: string
    ticket: Ticket
  }

  namespace SponsorParticipant {
      type SearchColumns = 'ticket.name' | 'ticket.email' | 'ticket.picName' | null
      interface SearchParams extends Partial<Nullable<Sponsor>>, ListParam {}
      interface ListParams extends ListParam, SearchParams {}
      interface CreateParams extends Omit<Sponsor, 'id', 'password' > {}
      interface UpdateParams extends Sponsor {}
  }
}
