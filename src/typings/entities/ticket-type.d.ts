/// <reference path="../global.d.ts"/>

namespace Entity {
  interface TicketType {
    id: number
    name: string
    eventId: number
  }

  namespace TicketType {
    interface SearchParams extends Partial<Nullable<TicketType>>, ListParam {}
    interface ListParams extends ListParam, SearchParams {}
    interface CreateParams extends Omit<TicketType, 'id' > {}
    interface UpdateParams extends TicketType {}

    interface Assignment {
      list: ListAssignemnt[]
    }
    interface ListAssignemnt {
      name: string
      total: number
    }
  }

}
