/// <reference path="../global.d.ts"/>

namespace Entity {
  interface License {
    id: number
    name: string
    licenseBlocks: LicenseBlock[]
  }

  interface LicenseType {
    id: number
    name: string
  }

  interface LicenseDetail extends Timestamp {
    id: number
    licenseBlockId: number
    amount: number
    remark: string
  }

  interface LicenseBlock extends Timestamp {
    isExpired: false
    total: number
    inUse: number
    available: number
    id: number
    companyId: number
    eventId: number
    licenseTypeId: number
    expiryDate: any
    description: string
    licenseType: LicenseType
    licenseDetails: LicenseDetail[]
  }
}
