/// <reference path="../global.d.ts"/>

namespace Entity {
  interface EmailCampaign extends Timestamp {
    id: number
    eventId: number
    emailTemplateTypeId: number
    name: string
    description: string
    supportEmail: string
    emailLayout: string
  }

  namespace EmailCampaign {
    interface SearchParams extends Partial<Nullable<Ticket>>, ListParam {}

    interface ListParams extends ListParam, SearchParams {}

    interface CreateUpdateParams {
      emailTemplateTypeId: number
      eventId: number
      name: string
      description: string
      supportEmail: string
      emailLayout: string
    }

    interface SendEmailParams {
      eventId: number
      emailCampaignId: number
      ticketIds: number[]
    }
  }
}
