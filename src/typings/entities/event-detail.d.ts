/// <reference path="../global.d.ts"/>

namespace Entity {
  interface EventDetail extends Timestamp {
    id: number
    eventId: number
    hallLayout?: array
    name: string
    checkInStartAt: any
    checkInEndAt: any
    pictureUrl?: string
  }

  namespace EventDetail {
    type SearchColumns = 'eventId' | null
    interface SearchParams extends Partial<Nullable<EventDetail>>, ListParam {}
    interface ListParams extends ListParam, SearchParams {}
    interface CreateParams extends Omit<EventDetail, 'id' | 'createdAt' | 'updatedAt'> {}
    interface UpdateParams extends Omit<EventDetail, 'createdAt' | 'updatedAt'> {}

    interface UpdateLayoutParams {
      eventDetailId: number
      hallLayout: array
    }
  }
}
