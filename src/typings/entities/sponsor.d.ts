/// <reference path="../global.d.ts"/>

namespace Entity {
  interface Sponsor {
    id: number
    eventId: number
    sponsorPackageId: number
    name: string
    picName: string
    picEmail: string
    picContactNumber: string
    picPosition: string
    amountReceived: number
    address: string
    customNote: string
    password: string

    sponsorPackage?: SponsorPackage
  }

  namespace Sponsor {
    type SearchColumns = 'name' | 'email' | 'picName' | null
    interface SearchParams extends Partial<Nullable<Sponsor>>, ListParam {}
    interface ListParams extends ListParam, SearchParams {}
    interface CreateParams extends Omit<Sponsor, 'id', 'password' > {}
    interface UpdateParams extends Sponsor {}

    interface SponsorLevel {
      name: string
      total: number
    }

    interface SponsorAmount {
      total: number
      pending: number
      collected: number
    }

    interface SponsorProgress {
      name: string
      pendingAmount: number
      totalAmount: number
      totalVendor: number
      collectedAmount: number
      collectedPercentage: number
    }

    interface VendorDashboard {
      totalVendor: number
      sponsorAmount: SponsorAmount
      sponsorPackages: SponsorLevel[]
      sponsorProgresses: SponsorProgress[]
    }
  }
}
