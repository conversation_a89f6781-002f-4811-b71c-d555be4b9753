/// <reference path="../global.d.ts"/>

namespace Entity {
  interface EmailCampaignLog extends Timestamp {
    id: number
    sentAt: string
    totalEmail: number
    fail: Summary
    success: Summary
    pending: boolean
  }

  interface Summary {
    total: number
    percent: number
  }

  namespace EmailCampaignLog {
    interface ListParams {
      emailCampaignId?: number
      event?: string
    }

    interface DownloadParams {
      emailLogId: number
      emailCampaignId: number
    }

    interface DownloadLink {
      csvFile: string
    }
  }
}
