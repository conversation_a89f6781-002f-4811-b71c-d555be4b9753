/// <reference path="../global.d.ts"/>

namespace Entity {
  interface EventDetailSummary {
    totalCheckIns: number
    totalLuckyDraws: number
    totalTables: number
    totalSeats: number
    eventDetail: EventDetail
  }

  interface EventSummary {
    eventId: number
    totalTickets: number
    totalUnsentTickets: number
    eventDetailSummary: EventDetailSummary[]
  }

  interface Summary {
    totalUsers: number
    totalCompanies: number
    totalEvents: number
    eventSummary: EventSummary[]
  }
}
