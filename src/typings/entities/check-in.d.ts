/// <reference path="../global.d.ts"/>

/* 角色数据库表字段 */
namespace Entity {
  interface CheckIn extends Timestamp {
    id: number
    ticketId: number
    eventDetailId: number
    ticket: Ticket
  }

  namespace CheckIn {
    type SearchColumns = 'name' | 'email' | null
    interface SearchParams extends Partial<Nullable<CheckIn>>, ListParam {}
    interface ListParams extends ListParam, SearchParams {}
    interface CreateParams {
      ticketNumber: string
      eventDetailId: number
    }
    interface UpdateParams extends Omit<CheckIn, 'createdAt' | 'updatedAt'> {}

    interface Summary extends Total {
      checkInInterval: array
      totalTicket: number
      totalCheckIn: number
      totalNotCheckIn: number
      totalCheckInTicketType: TicketType
    }

    interface TicketType extends Total {
      ticketType: string
    }

    interface Total {
      totalTicket: number
      totalCheckIn: number
    }
  }
}
