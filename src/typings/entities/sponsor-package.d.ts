/// <reference path="../global.d.ts"/>

namespace Entity {
  interface SponsorPackage {
    id: number
    eventId: number
    name: string
    amountSponsored: number
    speakingSlotDuration: number
    totalTickets: number
    totalBooths: number
    totalTables: number
    customNote: string
    sortOrder: number

    sponsors?: Sponsor[]
  }

  interface customKeyValue {
    key: string
    value: string
  }

  namespace SponsorPackage {
      type SearchColumns = 'name' | null
      interface SearchParams extends Partial<Nullable<SponsorPackage>>, ListParam {}
      interface ListParams extends ListParam, SearchParams {}
      interface CreateParams extends Omit<SponsorPackage, 'id' > {}
      interface UpdateParams extends SponsorPackage {}
  }
}
