/// <reference path="../global.d.ts"/>

/* 角色数据库表字段 */
namespace Entity {
  interface LuckyDraw extends Timestamp {
    id: number
    eventDetailId: number
    name: string
    description: string
    winnerQuantity: number
    isRepeatable: boolean
    prizeImage?: string
    ticketTypeIds: Array
    luckyDrawEligibleGroups?: Array
  }

  namespace LuckyDraw {
      type SearchColumns = 'name' | 'description' | null
      interface SearchParams extends Partial<Nullable<LuckyDraw>>, ListParam {}
      interface ListParams extends ListParam, SearchParams {}
      interface CreateParams extends Omit<LuckyDraw, 'id' | 'createdAt' | 'updatedAt'> {}
      interface UpdateParams extends Omit<LuckyDraw, 'createdAt' | 'updatedAt'> {}

      interface Winner {
        id: number
        luckyDrawId: number
        ticketId?: number
        isActive: boolean
        attempt: number
        ticket: Entity.Ticket
      }

      namespace Winner {
        interface CreateParams {
          luckyDrawId: number
          isCheckedIn: boolean
        }

        interface HistoryParams {
          luckyDrawId: number
          isActive: boolean
        }
      }
  }
}
