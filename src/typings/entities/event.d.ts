/// <reference path="../global.d.ts"/>

/* 角色数据库表字段 */
namespace Entity {
  interface Event extends Timestamp {
    id: number
    companyId?: number
    name: string
    description: string
    startAt: any
    endAt: any
  }

  namespace Event {
    type SearchColumns = 'name' | 'description' | null
    interface SearchParams extends Partial<Nullable<Event>>, ListParam {}
    interface ListParams extends ListParam, SearchParams {}
    interface CreateParams extends Omit<Event, 'id' | 'createdAt' | 'updatedAt'> {}
    interface UpdateParams extends Omit<Event, 'createdAt' | 'updatedAt'> {}
  }
}
