/// <reference path="../global.d.ts"/>

/* 角色数据库表字段 */
namespace Entity {
  interface Company extends Timestamp {
    id: number
    name: string
    addressLine1: string
    addressLine2: string
    city: string
    state: string
    postalCode: number
    countryId: number
    email: string
    callingCodeId: number
    phone: string
  }

  namespace Company {
    type SearchColumns = 'name' | 'email' | null
    interface SearchParams extends Partial<Nullable<Company>>, ListParam {}
    interface ListParams extends ListParam, SearchParams {}
    interface CreateParams extends Omit<Company, 'id' | 'createdAt' | 'updatedAt'> {}
    interface UpdateParams extends Omit<Company, 'createdAt' | 'updatedAt'> {}
  }
}
