/* 存放数据库实体表类型， 具体内容在 ./entities */
declare namespace Entity {
}

/* 各类接口返回的数据类型， 具体内容在 ./api */
declare namespace Api {

}

type Nullable<T> = { [P in keyof T]: T[P] | null }

interface Window {
  $loadingBar: import('naive-ui').LoadingBarApi
  $dialog: import('naive-ui').DialogApi
  $message: import('naive-ui').MessageApi
  $notification: import('naive-ui').NotificationApi
}

declare const AMap: any
declare const BMap: any

declare module '*.vue' {
  import type { DefineComponent } from 'vue'

  const component: DefineComponent
  export default component
}

declare namespace NaiveUI {
  type ThemeColor = 'default' | 'error' | 'primary' | 'info' | 'success' | 'warning'
}

declare namespace Storage {
  interface Session {
    dict: DictMap
  }

  interface Local {
    /* 存储用户信息 */
    userInfo: Api.Login.Info
    idToken: string
    /* 存储访问token */
    accessToken: string
    /* 存储刷新token */
    refreshToken: string
    /* 存储登录账号 */
    loginAccount: any
    /* 存储当前语言 */
    lang: App.lang
  }
}

declare namespace App {
  type lang = 'zhCN' | 'enUS'
}

interface DictMap {
  [key: string]: Entity.Dict[]
}

type ModalType = 'add' | 'edit'

interface Ilogin {
  email: string
  password: string
}

interface IChangePassword {
  email: string
}

// API
interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

interface PaginationInfo {
  currentPage: number
  pageSize: number
  totalPages: number
  totalItems: number
}

interface ListResponse<T> extends PaginationInfo {
  list: T[]
}

interface TicketListResponse<T> extends ListResponse<T> {
  template?: string
  emailCampaignId?: number
}

interface Criteria<T> {
  column: T | null
  type: 'equal' | 'contain' | 'notEqual' | null
  value: any | null
}

interface ListParam<T = string> {
  page?: number
  pageSize?: number
  keyword?: string | null
  columns?: T[]
  criteria?: Criteria<T>[]
  matchType: 'all' | 'any'
  sort?: Sort[]
}

interface Timestamp {
  createdAt: any
  updatedAt: any
}

interface Preview {
  template: string
  ticket: Entity.Ticket
}

interface PictureUrl {
  pictureUrl: string
}

interface File {
  fileName: string
  fileUrl: string
}

interface ExportFile {
  csvFile: string
}

interface ConfirmModalInfo {
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
}

type SortType = 1 | 0 // 1 = ASC, 0 = DESC
interface Sort {
  column: string
  order: SortType
}

interface customKeyValue {
  key: string
  value: string
}
