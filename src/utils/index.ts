export * from './icon'
export * from './storage'
export * from './array'
export * from './i18n'
export * from './dict'
export * from './date'

export function getUploadImageFileTypes() {
  return 'image/jpeg, image/png, image/webp, image/svg+xml, image/jpg'
}

export function pixelsToMm(pixels, dpi = 96) {
  return (pixels / dpi) * 25.4
}

export function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}
