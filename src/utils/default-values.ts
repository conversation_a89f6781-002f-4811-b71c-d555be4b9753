import type { SortOrder } from 'naive-ui/es/data-table/src/interface'

export function defaultCriteriaValue<T>(): Criteria<T>[] {
  const defaultSearchModel: Criteria<T>[] = [
    { column: null, type: null, value: null },
  ]

  return defaultSearchModel
}

export function preDefinedColors() {
  return [
    '#7F9A66',
    '#E8F5C2',
    '#A3C2D9',
    '#F8F5C2',
    '#FAD8C2',
    '#D1C2E8',
    '#C2D9A3',
    '#A3D9C2',
    '#FFFACD',
    '#FCE0D1',
    '#E0D1F8',
    '#BCE2A7',
    '#B1E0F8',
    '#F0FCE0',
    '#D9D1C2',
    '#C2B8A3',
    '#F8D1B1',
    '#C8E6C9',
    '#E0E0E0',
    '#FAFAD2',
    '#D1D1D1',
    '#AEE38C',
    '#FCE8D1',
    '#B8AEA3',
    '#E0F8FC',
  ]
}

export function getSortKey(sortOrder: SortOrder): SortType {
  let data: SortType = 1
  const map: { [key in 'ascend' | 'descend']: SortType } = {
    ascend: 1,
    descend: 0,
  }

  if (sortOrder) {
    data = map[sortOrder as 'ascend' | 'descend']
  }

  return data
}
