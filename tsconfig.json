{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["ESNext", "DOM"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["node", "vite/client", "naive-ui", "unplugin-icons/types/vue"], "allowJs": true, "strict": false, "strictNullChecks": true, "noUnusedLocals": true, "noEmitOnError": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "exclude": ["node_modules", "dist", "eslint.config.js"]}