import { resolve } from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import { createVitePlugins } from './build/plugins'
import { createViteProxy } from './build/proxy'
import { serviceConfig } from './service.config'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, __dirname, '') as ImportMetaEnv
  const envConfig = serviceConfig[mode as ServiceEnvType]

  // Define a version or timestamp
  const version = new Date().getTime().toString()

  const renameFilesWithVersion = true // Make sure to set this in your environment config

  return {
    base: env.VITE_BASE_URL,
    plugins: createVitePlugins(env),
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    server: {
      host: '0.0.0.0',
      proxy:
        env.VITE_HTTP_PROXY === 'Y' ? createViteProxy(envConfig) : undefined,
    },
    build: {
      target: 'esnext',
      reportCompressedSize: false,
      rollupOptions: {
        output: {
          entryFileNames: renameFilesWithVersion
            ? `assets/[name].${version}.js`
            : `assets/[name].js`,
          chunkFileNames: renameFilesWithVersion
            ? `assets/[name].${version}.js`
            : `assets/[name].js`,
          assetFileNames: renameFilesWithVersion
            ? `assets/[name].${version}[extname]`
            : `assets/[name][extname]`,
        },
      },
    },
    optimizeDeps: {
      include: ['echarts', 'md-editor-v3', 'quill'],
    },
  }
})
