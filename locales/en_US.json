{"common": {"back": "Back", "yes": "Yes", "no": "No", "setting": "Setting", "id": "ID", "cancel": "Cancel", "confirm": "Confirm", "close": "Closure", "reload": "Refresh", "choose": "<PERSON><PERSON>", "navigate": "Navigate", "inputPlaceholder": "please enter", "selectPlaceholder": "please choose", "action": "Action", "typeHere": "Type here ...", "select": "Select ...", "search": "Search", "addNew": "Add New", "edit": "Edit", "delete": "Delete", "modify": "Modify", "import": "Import", "export": "Export", "reset": "Reset", "submit": "Submit", "add": "Add", "confirmDelete": "Confirm Delete?", "createdAt": "Created At", "updatedAt": "Updated At", "dateTimeRange": "Date Time Range", "startAt": "Start At", "endAt": "End At", "detail": "Detail", "keyword": "Keyword", "filter": "Filter", "allOfTheFollowing": "All of the following", "anyOfTheFollowing": "Any of the following", "pleaseFill": "Please fill in all the information"}, "app": {"loginOut": "Logout", "loginOutContent": "Confirm to log out of current account?", "loginOutTitle": "Sign out", "userCenter": "Personal center", "lignt": "Light", "dark": "Dark", "system": "System", "backTop": "Back to top", "toggleSider": "Toggle sidebar", "BreadcrumbIcon": "Breadcrumbs icon", "blackAndWhite": "Black and white mode", "bottomCopyright": "Bottom copyright", "breadcrumb": "Bread crumbs", "colorWeak": "Color Weakness Mode", "interfaceDisplay": "Interface display", "logoDisplay": "LOGO display", "messages": "Messages", "multitab": "Display multiple tabs", "notifications": "Notify", "notificationsTips": "Notification", "pageTransition": "Page transition", "reset": "Reset", "resetSettingContent": "Confirm to reset all settings?", "resetSettingMeaasge": "Reset successful", "resetSettingTitle": "Reset settings", "searchPlaceholder": "Search page/path", "setting": "Setting", "systemSetting": "System settings", "themeColor": "Theme color", "themeSetting": "Theme settings", "todos": "Todos", "toggleFullScreen": "Toggle full screen", "togglContentFullScreen": "Toggle content full screen", "topProgress": "Top progress", "transitionFadeBottom": "Bottom fade", "transitionFadeScale": "Scale fade", "transitionFadeSlide": "Side fade", "transitionNull": "No transition", "transitionSoft": "Soft", "transitionZoomFade": "Expand fade out", "transitionZoomOut": "Zoom out", "watermake": "Watermark", "closeOther": "Close other", "closeAll": "Close all", "closeLeft": "Close left", "closeRight": "Close right", "backHome": "Back to the homepage", "getRouteError": "Failed to obtain route, please try again later.", "layoutSetting": "Layout settings", "leftMenu": "Left menu", "topMenu": "Top menu", "mixMenu": "Mix menu", "switchCompany": "Switch Company"}, "login": {"signInTitle": "<PERSON><PERSON>", "accountRuleTip": "Please enter account", "passwordRuleTip": "Please enter password", "or": "Or", "rememberMe": "Remember me", "forgotPassword": "Forget the password?", "signIn": "Sign in", "signUp": "Sign up", "noAccountText": "Don't have an account?", "accountPlaceholder": "Enter your email", "checkPasswordPlaceholder": "Please enter password again", "checkPasswordRuleTip": "Please confirm password again", "haveAccountText": "Do you have an account?", "passwordPlaceholder": "Enter password", "readAndAgree": "I have read and agree", "registerTitle": "Register", "userAgreement": "User Agreement", "resetPassword": "Reset password", "resetPasswordPlaceholder": "Enter account/mobile phone number", "resetPasswordRuleTip": "Please enter your account/mobile phone number", "resetPasswordTitle": "Reset"}, "route": {"appRoot": "Home", "cardList": "Card list", "commonList": "Common list", "dashboard": "Dashboard", "company": "Company", "event": "Event", "eventDetail": "Event Detail", "ticketType": "Ticket Type", "ticket": "Ticket", "approval": "Approval", "ticketPreview": "Ticket Preview", "checkIn": "Check In", "luckyDraw": "Lucky Draw", "demo": "Function example", "fetch": "Request example", "list": "List", "monitor": "Monitoring", "test": "Multi-level menu", "test2": "Multi-level menu subpage", "test2Detail": "Details page of multi-level menu", "test3": "multi-level menu", "test4": "Multi-level menu 3-1", "workbench": "Workbench", "QRCode": "QR code", "about": "About", "clipboard": "Clipboard", "demo403": "403", "demo404": "404", "demo500": "500", "dictionarySetting": "Dictionary settings", "documents": "Document", "documentsVite": "Vite", "documentsVue": "<PERSON><PERSON>", "documentsVueuse": "VueUse (external link)", "echarts": "Echarts", "editor": "Editor", "editorMd": "MarkDown editor", "editorRich": "Rich text editor", "error": "Exception page", "icons": "Icon", "justSuper": "Supervisible", "map": "Map", "menuSetting": "<PERSON><PERSON>", "permission": "Permissions", "permissionDemo": "Permissions example", "setting": "System settings", "userCenter": "Personal Center", "accountSetting": "User settings", "cascader": "Administrative region selection", "dict": "Dictionary example"}, "http": {"400": "Syntax error in the request", "401": "User unauthorized", "403": "Server refused access", "404": "Requested resource does not exist", "405": "Request method not allowed", "408": "Network request timed out", "422": "Unprocessable Entity", "500": "Internal server error", "501": "Server not implemented the requested functionality", "502": "Bad gateway", "503": "Service unavailable", "504": "Gateway timeout", "505": "HTTP version not supported for this request", "defaultTip": "Request error"}, "components": {"iconSelector": {"inputPlaceholder": "Select target icon", "searchPlaceholder": "Search icon", "clearIcon": "Clear icon", "selectorTitle": "Icon selection"}, "copyText": {"message": "<PERSON><PERSON>d successfully", "tooltip": "Copy"}}, "company": {"name": "Name", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "city": "City", "state": "State", "postalCode": "Postal Code", "country": "Country", "email": "Email", "callingCode": "Calling Code", "phone": "Phone", "createdAt": "Created At", "updatedAt": "Updated At", "selectCompany": "Select Company", "switchCompanyMessage": "Select the appropriate company to access data relevant to your account"}, "event": {"name": "Name", "description": "Description", "startAt": "Start At", "endAt": "End At", "ticketType": "Ticket Type", "checkInStartAt": "Check In Start At", "checkInEndAt": "Check In End At", "createSuccess": "Event Created Successfully!"}, "eventDetail": {"event": "Event", "hallLayout": "Hall Layout", "checkIn": "Check In", "name": "Name"}, "ticketType": {"name": "Name"}, "ticket": {"name": "Name", "email": "Email", "ticketType": "Ticket Type", "contactNumber": "Contact Number", "companyName": "Company Name", "totalEmailSent": "Total Email Sent", "ticketNumber": "Ticket Number", "checkInAt": "Check In At", "customKey": "Custom Key", "customValue": "Custom Value", "gender": "Gender", "age": "Age", "status": "Status"}, "luckyDraw": {"name": "Name", "description": "Description", "winnerQuantity": "Winner Quantity", "isRepeatable": "Repeatable", "eligibleGroups": "Eligible Groups", "totalEmailSent": "Total Email Sent", "ticketNumber": "Ticket Number", "checkInAt": "Check In At", "customKey": "Custom Key", "customValue": "Custom Value"}}