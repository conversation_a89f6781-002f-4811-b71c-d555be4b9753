{"name": "nova-admin", "type": "module", "version": "0.9.6", "private": true, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a", "description": "a clean and concise back-end management template based on Vue3, Vite5, Typescript, and Naive UI.", "author": {"name": "chansee97", "email": "<EMAIL>", "url": "https://github.com/chansee97"}, "license": "MIT", "homepage": "https://github.com/chansee97/nova-admin", "repository": {"url": "https://github.com/chansee97/nova-admin.git"}, "bugs": {"url": "https://github.com/chansee97/nova-admin/issues"}, "keywords": ["<PERSON><PERSON>", "Vue3", "admin", "admin-template", "vue-admin", "vue-admin-template", "Vite5", "Vite", "vite-admin", "TypeScript", "TS", "NaiveUI", "naive-ui", "naive-admin", "NaiveUI-Admin", "naive-ui-admin", "UnoCSS"], "scripts": {"dev": "vite --mode dev --port 9980", "dev:test": "vite --mode test", "dev:prod": "vite --mode prod", "build": "vue-tsc --noEmit || true && vite build --mode prod || true", "build:dev": "vue-tsc --noEmit && vite build --mode dev", "build:test": "vue-tsc --noEmit && vite build --mode test", "preview": "vite preview --port 9981", "lint": "eslint . && vue-tsc --noEmit", "lint:fix": "eslint . --fix", "lint:check": "npx @eslint/config-inspector", "sizecheck": "npx vite-bundle-visualizer"}, "dependencies": {"@alova/scene-vue": "^1.6.0", "@leafer-in/view": "^1.6.1", "@leafer-in/viewport": "^1.6.1", "@noction/vue-draggable-grid": "^1.11.0", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vueuse/core": "^10.11.0", "alova": "^2.21.3", "axios": "^1.7.2", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.7", "chartjs-plugin-datalabels": "^2.2.0", "colord": "^2.9.3", "echarts": "^5.5.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "http-proxy-agent": "^7.0.2", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "leafer-ui": "^1.6.1", "leaflet": "^1.9.4", "md-editor-v3": "^4.15.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "pdfmake": "^0.2.14", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "pusher": "^5.2.0", "pusher-js": "8.4.0-rc2", "qs": "^6.13.0", "quill": "^2.0.2", "radash": "^12.1.0", "vue": "^3.4.33", "vue-chartjs": "^5.3.2", "vue-i18n": "^9.13.1", "vue-qrcode-reader": "^5.7.1", "vue-router": "^4.4.0"}, "devDependencies": {"@antfu/eslint-config": "^2.23.0", "@iconify-json/icon-park-outline": "^1.1.15", "@iconify/vue": "^4.1.2", "@types/node": "^20.14.11", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "eslint": "^9.7.0", "lint-staged": "^15.2.7", "naive-ui": "^2.39.0", "sass": "^1.77.8", "simple-git-hooks": "^2.11.1", "typescript": "^5.5.3", "unocss": "^0.61.5", "unplugin-auto-import": "^0.18.0", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.3", "vite": "^5.3.4", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "7.3.6", "vue-tsc": "^2.0.26"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "volta": {"node": "20.12.2"}}