/** 不同请求服务的环境配置 */
export const serviceConfig: Record<ServiceEnvType, Record<string, string>> = {
  dev: {
    url: 'https://mock.apifox.cn/m1/4071143-0-default',
    mainApi: 'http://localhost:3000/development/api',
    authApi: 'http://localhost:4000/development/api/auth/users',
  },
  test: {
    url: 'https://mock.apifox.cn/m1/4071143-0-default',
  },
  prod: {
    url: 'https://mock.apifox.cn/m1/4071143-0-default',
    mainApi: 'https://o4xk6fu9v7.execute-api.ap-southeast-1.amazonaws.com/development/api',
    authApi: 'https://hwz3z3eirc.execute-api.ap-southeast-1.amazonaws.com/development/api/auth/users',
  },
}
